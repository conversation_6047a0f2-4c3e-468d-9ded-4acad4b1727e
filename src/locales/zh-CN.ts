export default {
  'hooks.Authorization.loginFailedMessage': '登录失败',
  'service.Me.anonymousName': '路人',
  'Common.loadingText': '系统正在操作，请稍候...',
  'Common.confirm': '确定',
  'Common.cancel': '取消',
  'File.file': '文件',
  'File.document': '文档',
  'File.newdoc': '文档',
  'File.modoc': '传统文档',
  'File.mosheet': '表格',
  'File.table': '应用表格',
  'File.presentation': '幻灯片',
  'File.form': '表单',
  'File.normalForm': '普通表单',
  'File.tableViewForm': '表格视图表单',
  'File.quizForm': '测验表单',
  'File.folder': '文件夹',
  'Login.loginTitle': '登录',
  'Login.autoLanguageSetting': '跟随系统',
  'Login.autoThemeSetting': '跟随系统',
  'Login.lightThemeSetting': '亮色模式',
  'Login.darkThemeSetting': '暗色模式',
  'LoginView.userNameInputPlaceholder': '输入邮箱',
  'LoginView.passwordInputPlaceholder': '输入密码',
  'LoginView.loginButtonText': '登录',
  'LoginView.userNameInputLabel': '邮箱',
  'LoginView.passwordInputLabel': '密码',
  'LoginView.emailFormatInaccurate': '邮箱格式不准确',
  'MessageCenter.onlyreadButtonText': '只看已读',
  'MessageCenter.onlyUnreadButtonText': '只看未读',
  'MessageCenter.allMarkReadButtonText': '全部标记未已读',
  'UserCenter.settings': '个人设置',
  'UserCenter.myBusiness': '我的企业',
  'UserCenter.switchLanguages': '界面语言',
  'UserCenter.logOut': '退出登陆',
  'UserCenter.myDesktopCapacit': '我的桌面容量',
  'UserCenter.totalEnterpriseCapacity': '企业总容量',
  'SearchCenter.me': '我',
  'SearchCenter.update': '更新',
  'SearchCenter.open': '打开',
  'SearchCenter.searchFile': '搜索文件',
  'SearchCenter.noData': '无搜索结果',
  'SearchCenter.used': '最近使用',
  'SearchCenter.search': '搜索结果',
  'Header.backButtonTipText': '返回',
  'Header.backToButtonTipText': '返回到',
  'Header.createButtonTipText': '新建',
  'Header.inputPlaceholder': '无标题',
  'Header.teamButtonText': '协作',
  'Header.shareButtonText': '分享',
  'Header.historyButtonText': '历史',
  'Header.demoButtonText': '演示',
  'Header.fileMenuButtonTipText': '文件菜单',
  'Header.editButtonText': '编辑',
  'Header.downloadButtonText': '下载',
  'Header.sharingCollaborationButtonText': '分享协作',

  'Editor.saveStatus.offlineSaving': '正在保存离线内容',
  'Editor.saveStatus.offlinePersistSucceed': '编辑已离线保存，连接到网络后会自动同步',
  'Editor.saveStatus.offline': '未连接到网络，内容将离线保存',
  'Editor.saveStatus.offlinePersistFailed': '离线保存失败',
  'Editor.saveStatus.online': '内容将自动保存',
  'Editor.saveStatus.onlineSaving': '正在保存',
  'Editor.saveStatus.saveAccepted': '正在保存',
  'Editor.saveStatus.saveSucceed': '自动保存成功',
  'Editor.saveStatus.applySucceed': '内容已自动更新',
  'Editor.saveStatus.saveFailed': '保存失败',
  'Editor.saveStatus.applyFailed': '保存失败',
  'Editor.saveStatus.saveTimeout': '保存失败',
  'Editor.saveStatus.acceptTimeout': '保存失败',
  'Editor.syncStatus.syncSaving': '正在同步离线数据，请稍等...',
  'Editor.syncStatus.syncSucceed': '离线数据同步成功，内容将自动保存',
  'Editor.syncStatus.syncFailed': '保存失败',
  'Editor.noSupport': '该功能暂不支持',
  'Editor.ok': '知道了',
  'Editor.syncSaving': '正在同步离线数据，请稍等...',
  'Editor.syncSucceed': '离线数据同步成功。',
  'Editor.syncFailed': '离线数据同步失败。',
  'Editor.noEditing': '系统已禁止编辑',
  'Editor.noEditingContent': '您没有该文档的编辑权限，请联系文件管理员',
  'Editor.sorry': '抱歉...',
  'Editor.fileDeleted': '文件已被删除',
  'Editor.saveFailed': '保存失败',
  'Editor.saveFailedContent': '保存失败，请复制当前编辑数据，刷新后继续使用',

  'Header.favorite': '收藏',
  'Header.unfavorite': '取消收藏',
  'Header.favorited': '已收藏',
  'Header.unfavorited': '已取消收藏',
  'BackToPopover.searchFiles': '搜索文件...',
  'BackToPopover.backTo': '返回到',
  'BackToPopover.myDesktop': '我的桌面',
  'BackToPopover.workbench': '工作台',
  'BackToPopover.recentlyUsed': '最近使用',
  'BackToPopover.quickAccess': '快速访问',
  'AvatarGroup.restCount': '还有{count}个协作者',

  'SiderMenu.siderMenuCreactText': '创建',
  'SiderMenu.createDisablePopText': '当前文件夹无编辑权限，无法新建文件夹，请联系该文件夹管理者',
  'SiderMenu.new': '新建',
  'SiderMenu.siderMenuRecentText': '最近文件',
  'SiderMenu.siderMenuShareText': '共享给我',
  'SiderMenu.siderMenuFavoritesText': '我的收藏',
  'SiderMenu.siderMenuDesktopText': '我的桌面',
  'SiderMenu.siderMenuSpaceText': '团队空间',
  'SiderMenu.siderMenuTrashText': '回收站',
  'SiderMenu.siderMenuBusinessText': '企业管理',
  'SiderMenu.siderMenuCreateDocText': '文档',
  'SiderMenu.siderMenuCreateMoDocText': '传统文档',
  'SiderMenu.siderMenuCreateTableText': '表格',
  'SiderMenu.siderMenuCreateMoTableText': '应用表格',
  'SiderMenu.siderMenuCreatePptText': '幻灯片',
  'SiderMenu.siderMenuCreateFormText': '表单',
  'SiderMenu.siderMenuCreateOrdinaryFormText': '普通表单',
  'SiderMenu.siderMenuCreateTableFormText': '表格视图表单',
  'SiderMenu.siderMenuCreateTestFormText': '测验表单',
  'SiderMenu.siderMenuCreateFolderText': '文件夹',
  'SiderMenu.siderMenuCreateSpace': '空间',
  'SiderMenu.siderMenuCreateBlank': '白板',
  'SiderMenu.siderMenuCreateThink': '思维导图',
  'SiderMenu.siderMenuCreateCloud': '云文件',
  'SiderMenu.siderMenuCreateSpaceText': '团队空间',
  'SiderMenu.siderMenuCreateUploadFolderText': '上传文件夹',
  'SiderMenu.siderMenuCreateUploadFileText': '上传文件',
  'SiderMenu.siderMenuCreateFileUploadText': '文件上传',
  'SiderMenu.siderMenuCreateTemplateText': '从模版库创建',
  'SiderMenu.siderMenuTemplateCreateText': '模版库创建',
  'SiderMenu.cancel': '取消',
  'SiderMenu.confirm': '确认',
  'SiderMenu.inputName': '输入名称',

  'CreateFileMenu.networkStatusTipText': '请检查您的网络',
  'CreateFolder.folderTitle': '新建文件夹',
  'CreateFolder.saveVersionTitle': '保存版本',
  'CreateFolder.spaceTitle': '新建团队空间',
  'CreateFolder.folderPlaceholder': '请输入文件夹名称',
  'CreateFolder.spacePlaceholder': '请输入空间名称',
  'CreateFolder.saveVersionPlaceholder': '请输入保存版本名称',
  'CreateFolder.ruleMessage': '请填写完整信息',
  'Editor.openInNewTab': '在新标签页打开',
  'AddNewPopover.templateLibrary': '模板库',
  'AddNewPopover.upload': '上传',
  'AddNewPopover.uploadFolder': '上传文件夹',

  'Error.loginGuideTitle': '请登录后访问',
  'Error.loginText': '登录',
  'Error.notFound': '页面不存在',
  'Error.notFoundDes': '您访问的页面不存在',
  'Error.goBackHomePage': '返回桌面',
  'Error.fileDeleteTitle': '文件被删除',
  'Error.fileDeleteSubTitle': '您访问的文件已被删除',
  'Error.switchAccount': '切换账号',
  'Error.accessRestrictedTitle': '需要登录访问',
  'Error.noSeatsTitle': '没有固定席位',
  'Error.noSeatsTitleDes': '您尚未获得访问席位，请联系管理员分配或购买席位',
  'Error.contactAdmin': '联系管理员',
  'Error.purchaseSeats': '购买席位',
  'Error.unknownErrorTitle': '页面未成功加载',
  'Error.unknownErrorSubTitle': '请确认网络良好的状态下，刷新页面重试',
  'Error.netErr': '网络异常',
  'Error.netErrDes': '请检查网络，重新访问',
  'Error.refresh': '刷新',
  'Error.noLogin': '您还没有登录',
  'Error.noLoginDes': '文档所有者已设置权限为特定用户可访问，请登录后再尝试',
  'Error.noPermission': '没有权限访问',
  'Error.noPermissionDes': '文档所有者未添加你协作，请联系所有者开启访问权限',
  'Error.noPermissionDesH5': '当前登录帐号{user}没有权限访问这个文档',
  'Error.applyPermissionSent': '已向管理者发送申请',
  'Error.applyPermissionSentSuccess': '你的申请已发送成功',
  'Error.applyForPermissionBtn': '申请权限',
  'Error.applyTooFrequent': '申请发送频繁，请两分钟后再试',
  'Error.applyForPermission': '申请访问权限',
  'Error.selectPermissionType': '请选择申请的权限类型',
  'Error.addRemarkPlaceholder': '如有需要，可在此添加备注',
  'Error.sendApplication': '发送申请',
  'Error.permissionReadable': '可阅读',
  'Error.permissionCommentable': '可评论',
  'Error.permissionEditable': '可编辑',

  // 用户被禁用页面
  'Forbidden.title': '账号已停用',
  'Forbidden.tips': '{email}，您的企业账号已被停用，如需登录，请联系您的企业管理员恢复您的账号。',

  'deleteConfirm.title': '确认删除',
  'deleteConfirm.content': '确认删除文件？删除后所有协作者都无法访问此文件。',
  'deleteConfirm.cancel': '取消',
  'deleteConfirm.success': '删除成功',
  'deleteConfirm.error': '删除失败',
  'useFileDetail.creator': '创建者',
  'useFileDetail.modocTitle': '文档信息',
  'useFileDetail.mosheetTitle': '表格信息',
  'useFileDetail.tableTitle': '应用表格信息',
  'useFileDetail.pptTitle': '幻灯片信息',
  'useFileDetail.formTitle': '表单信息',
  'useFileDetail.statisticWordCount': '总字数',
  'useFileDetail.charCount': '字符数（计空格）',
  'useFileDetail.charCountWithoutSpaces': '字符数（不计空格）',
  'useFileDetail.page': '页数',
  'useFileDetail.total': '总字数',
  'useFileDetail.paragraphCount': '段落数',
  'useFileDetail.sections': '节数',
  'useFileDetail.views': '阅读次数',
  'useFileDetail.times': '次',
  'useFileDetail.imgTitle': '图片标题',
  'RenameModal.edit': '修改',
  'RenameModal.editSuccess': '修改成功',
  'RenameModal.editError': '修改失败',
  'RenameModal.title': '文件重命名',
  'RenameModal.InputPlaceholder': '请输入文件名称',
  'RenameModal.validatorMessage': '文件名中不得包含以下非法字符',
  'formatTime.justNow': '刚刚',
  'formatTime.minutesAgo': '{minutes} 分钟前',
  'formatTime.today': '今天 {hhmm}',
  'formatTime.yesterday': '昨天 {hhmm}',
  'File.setFilter': '设置筛选',
  'File.clearTrash': '清空回收站',
  'File.clearTrashSuccess': '清空回收站成功',
  'File.clearTrashError': '清空回收站失败',
  'File.clearTrashWarn': '是否确认清空回收站，确认将无法找回，请谨慎操作！',
  'File.clearTrashTips': '温馨提示：回收站内的文件也属于企业的数字资产，企业有权进行回收',
  'File.resetFirst': '文件已恢复至原位置',
  'File.resetError': '恢复失败',
  'File.checkedTotal': '已选中 {checked} 个文件',
  'File.fileName': '文件名',
  'File.createName': '创建者',
  'File.updateTime': '最近更新',
  'File.updatedAt': '更新时间',
  'File.createdAt': '创建时间',
  'File.openTime': '打开时间',
  'File.editTime': '编辑时间',
  'File.fileSize': '大小',
  'File.uncheck': '取消选中',
  'File.allCheck': '全选',
  'File.move': '移动',
  'File.delete': '删除',
  'File.more': '更多',
  'File.deleteSuccess': '删除成功',
  'File.deleteError': '删除失败',
  'File.deleteTips': '批量删除文件上限为{max}个',
  'File.newTabOpens': '新标签页打开',
  'File.edit': '编辑',
  'File.star': '添加到我的收藏',
  'File.starSuccess': '添加到我的收藏成功',
  'File.starError': '添加到我的收藏失败',
  'File.removeStar': '从我的收藏移除',
  'File.removeSuccess': '从我的收藏移除成功',
  'File.removeError': '从我的收藏移除失败',
  'File.share': '分享',
  'File.view': '预览',
  'File.shareInfo': '共享信息',
  'File.collaboration': '协作',
  'File.download': '下载',
  'File.downloadSuccess': '下载成功',
  'File.downloadError': '下载失败',
  'File.png': '图片',
  'File.reName': '重命名',
  'File.moveTo': '移动到',
  'File.copyTo': '创建副本到',
  'File.clearRecord': '清空此条记录',
  'File.clearFilter': '清除筛选',
  'File.recentlyOpened': '最近打开',
  'File.recentlyEdit': '最近编辑',
  'File.deleteTime': '删除时间',
  'File.recover': '恢复文件',
  'File.deleteCompletely': '彻底删除',
  'File.noShareTitle': '还没有收到任何协作邀请',
  'File.noShareDescription': '你可以在这里看到你被添加成为协作者的文件和文件夹',
  'File.noFavoritesTitle': '暂无收藏',
  'File.noFavoritesDescription': '你可以在这里看到你收藏的文件和文件夹',
  'File.noDesktopTitle': '还没有创建任何文件',
  'File.noDesktopDescription': '你可以在这里看到自己创建的文件',
  'File.noTrashTitle': '还没有任何垃圾和文件',
  'File.noTrashDescription': '你可以在这里看到被删除的文件和文件夹，这里的文件和文件夹都可以恢复到原始位置',
  //文件列表 h5
  'File.defaultSort': '默认排序',
  'File.sortByFileName': '按文件名排序',
  'File.sortByCreationTime': '按创建时间排序',
  'File.byNewnessSortBy': '按更新时间排序',
  'File.noRecentlyUsedFiles': '无最近使用文件',
  'File.folderAtTop': '文件夹置顶',

  'MessageCenter.commented': '评论了',
  'MessageCenter.mentioned': '提到了你',
  'MessageCenter.addCollaborator': '添加你为{invitedRole}',
  'MessageCenter.setAdministrator': '将你设置为企业管理员',
  'MessageCenter.inviteJoinBusiness': '邀请你加入企业',
  'MessageCenter.newMembersJoin': '新成员加入',
  'MessageCenter.deleteDocument': '删除文档',
  'MessageCenter.remindsReviewTasks': '提醒你查看任务',
  'MessageCenter.liked': '点赞了',
  'MessageCenter.NotificationToDoChanges': '删除了你在',
  'MessageCenter.dateArrived': '日期到了',
  'MessageCenter.mentionYou': '提到你',
  'MessageCenter.moveYouOfBusiness': '将你移出企业',
  'MessageCenter.handingOverBusinessToYou': '将企业移交给你',
  'MessageCenter.companyNameChanged': '修改了企业名称“{name}”',
  'MessageCenter.openedBusinessLink': '评论打开企业邀请链接了',
  'MessageCenter.closedBusinessLink': '关闭了企业邀请链接',
  'MessageCenter.taskReminders': '提醒你查看任务',
  'MessageCenter.tableSelectionReminders': '表格选区提醒',
  'MessageCenter.changeUserConfig': '修改企业成员账号（邮箱，昵称，密码）',
  'MessageCenter.systemNotifications': '系统通知',
  'MessageCenter.application': '申请',
  'MessageCenter.markRead': '标记为已读',
  'MessageCenter.join': '加入',
  'MessageCenter.discussion': '的讨论',
  'MessageCenter.permissions': '权限',
  'File.downloadErr': '文件下载失败，请稍后再试',
  'File.startDownload': '开始下载',
  'File.startImport': '文件开始导入',
  'File.isLoading': '文件正在加载中, 请稍后',
  'File.importSuccess': '文件导入成功',
  'File.importErr': '文件导入失败',
  'File.importTypeErr': '文件导入类型错误',
  'File.isLastImg': '已经是最后一张图片了',
  'File.noData': '暂无数据',
  'File.unknownParentDirectory': '未知上级目录 / ',
  'File.noPermissionToViewParentDirectory': '无权限查看上级目录',

  'MessageCenter.basicInformationModification': '基本信息修改',
  //团队空间
  'Space.countSpace': '个空间',
  'Space.createSpace': '新建空间',
  'Space.createTeamSpace': '新建团队空间',
  'Space.sure': '确定',
  'Space.cancel': '取消',
  'Space.enterSpaceName': '请输入空间名称',
  'Space.SpaceNameHeader': '空间名称',
  'Space.infoSuccess': '创建成功',
  'Space.infoEmpty': '空间名称不能为空',
  'Space.infoWaring1': '开头不能有空格，已自动去除',
  'Space.infoWaring2': '空间名称不能超过20个字符',
  'Space.rightClickShare': '分享',
  'Space.rightClickCollaboration': '协作',
  'Space.rightClickSetting': '设置',
  'Space.rightClickDelete': '删除',
  'Space.infoEditSuccess': '修改成功',
  'Space.teamspaceSetting': '团队空间设置',
  'Space.teamspaceOwnership': '所属权',
  'Space.teamspaceWhatOwnership': '所属权是什么?',
  'Space.teamspaceBelongsto': '该空间的所有权归属于',
  'Space.teamspaceOwnershipExp':
    '所有权决定了团队空间里的文件被谁管理。如果所有权归属于企业，则会受到企业安全设置的影响，被审计系统收录，并参入效能看板的统计。',
  'Space.teamspaceConfirmDeletion': '确认删除',
  'Space.teamspaceDeleteSuccess': '删除成功',
  'Space.teamspaceDeleteTipText': '确认删除团队空间吗？删除后，所有协作者都无法访问此团队空间',
  'Space.noTeamspace': '暂无团队空间',
  'Space.noTeamspaceTipText': '点击左上角新建按钮为你的团队新建一个空间',
  //个人设置
  'Profile.title': '个人设置',
  'Profile.accountInfo': '账号信息',
  'Profile.preferenceSitting': '偏好设置',
  'Profile.accountID': '账号 ID',
  'Profile.modifyInfo': '修改基本信息',
  'Profile.safetySetting': '安全设置',
  'Profile.accountPd': '密码',
  'Profile.modify': '修改',
  'Profile.modifyImg': '修改头像',
  'Profile.uploadImg': '上传头像',
  'Profile.nickName': '昵称',
  'Profile.enterNickname': '请输入昵称',
  'Profile.nickNameRule': '昵称不能超过20个字符',
  'Profile.modifySuccess': '修改用户信息成功',
  'Profile.modifyFailed': '修改用户信息失败',
  'Profile.getUploadToken': '获取上传图片token失败',
  'Profile.uploadImgSuccess': '上传图片成功',
  'Profile.uploadImgFailed': '上传图片失败',
  'Profile.changePd': '修改密码',
  'Profile.forgetPd': '忘记密码',
  'Profile.currentPassword': '当前密码',
  'Profile.newPassword': '设置密码',
  'Profile.confirmNewPassword': '确认新密码',
  'Profile.currentPasswordRequired': '必须填写当前密码',
  'Profile.currentPasswordPlaceholder': '请输入当前密码',
  'Profile.newPasswordPlaceholder': '请输入新密码',
  'Profile.newPasswordRequired': '输入新密码',
  'Profile.newPasswordLength8': '密码不能少于 8 个字符',
  'Profile.newPasswordRule': '密码必须包含数字、英文大写和小写字母',
  'Profile.confirmNewPasswordPlaceholder': '请再次输入新密码',
  'Profile.confirmNewPasswordRequired': '请确认新密码',
  'Profile.confirmNewPasswordMatch': '两次输入的密码不一致',
  'Profile.changePdSuccess': '修改密码成功',
  'Profile.changePdFailed': '你输入的密码有误',
  'Profile.uploadImgRuleType': '只能上传 JPG、PNG、GIF、JPEG 格式的图片',
  'Profile.uploadImgRuleSize': '图片大小不得超过 2MB',
  'Profile.networkError': '网络响应异常',
  'ManagementSiderMenu.backDesktopTest': '返回主页',
  'FileMenuPopover.favorite': '收藏',
  'FileMenuPopover.move': '移动',
  'FileMenuPopover.createCopy': '创建副本',
  'FileMenuPopover.download': '下载',
  'FileMenuPopover.print': '打印',
  'FileMenuPopover.saveVersion': '保存版本',
  'FileMenuPopover.viewHistory': '查看历史',
  'FileMenuPopover.viewCommentList': '查看评论列表',
  'FileMenuPopover.help': '帮助',
  'FileMenuPopover.docGuide': '文档使用指南',
  'FileMenuPopover.docShortcut': '文档快捷键',
  'FileMenuPopover.mosheetGuide': '表格使用指南',
  'FileMenuPopover.mosheetShortcut': '表格快捷键',
  'FileMenuPopover.delete': '删除',
  'FileMenuPopover.downImage': '图片',
  'FileMenuPopover.downWord': 'Word',
  'FileMenuPopover.downPDF': 'PDF',
  'FileMenuPopover.downMarkdown': 'Markdown',
  'FileMenuPopover.downWPS': 'WPS',
  'FileMenuPopover.downImagePDF': '纯图PDF',
  'FileMenuPopover.downExcel': 'Excel',
  'FileMenuPopover.downZip': 'ZIP',
  'FileMenuPopover.downPPTX': 'PPTX',
  'FileMenuPopover.convertToMoSheet': '转换为石墨表格',
  'FileMenuPopover.downToExcel': '下载为Excel',
  'FileMenuPopover.tableHelp': '应用表格帮助中心',
  'FileMenuPopover.addComment': '添加评论',
  'FileMenuPopover.viewComment': '查看评论',
  'FileMenuPopover.formHelp': '表单帮助中心',
  'FileMenuPopover.documentInfo': '文档信息',
  'FileMenuPopover.fileInfo': '文件信息',
  'FileMenuPopover.noMovePermissionTip': '你没有权限移动文件，请联系文件管理者或企业管理员',
  'FileMenuPopover.noCreateCopyPermissionTip': '你没有权限创建副本，请联系文件管理者或企业管理员',

  'UploadBoard.uploadSuccessTitle': '上传完成',
  'UploadBoard.uploadingTitle': '正在上传',
  'UploadBoard.uploadFailTitle': '发生错误',
  'UploadBoard.uploadCancelTitle': '项已取消',
  'UploadBoard.uploadConfirmCancelTitle': '确定取消上传？',
  'UploadBoard.uploadConfirmCancelContent': '关闭面板将取消正在上传的文件，已上传成功的文件不受影响。确定要取消上传？',
  'UploadBoard.uploadConfirmCancelOkText': '取消上传',
  'UploadBoard.uploadConfirmCancelCancelText': '继续上传',
  'UploadBoard.uploadFailTipTitleText': '部分文件上传失败，请重新上传或复制以下错误信息提交客服处理:',
  'UploadBoard.uploadFailMessageText': '错误信息',
  'UploadBoard.uploadCopyFailMessageText': '复制错误信息',
  'UploadBoard.uploadCopySuccessText': '复制成功',
  'UploadBoard.uploadCheckFailMessageText': '查看错误信息',
  'UploadBoard.uploadRetryText': '重试',
  'UploadBoard.uploadOpenFolderText': '打开所在文件夹',
  'UploadBoard.uploadStatusTipCancelText': '已取消',
  'UploadBoard.uploadExpTipRetractText': '收起',
  'UploadBoard.uploadExpTipExpandText': '展开',
  'UploadBoard.uploadExpTipRetractErrorMessageText': '收起错误信息',
  'UploadBoard.failTipNum': '{number}个',
  'UploadBoard.failTipTitle': '检测到未知错误，以下共{number}条文件请单独上传或复制错误信息提交客服处理:',
  'UseFileUpload.networkError': '网络异常',
  'UseFileUpload.noSpaceTitle': '空间内存不足',
  'UseFileUpload.noSpaceContent': '您的空间内存不足，请联系客服或销售来增购空间容量；',
  'UseFileUpload.noSpaceOkText': '确定',

  // 企业管理
  'Management.backDesktop': '返回主站',
  'Management.enterpriseManagementSystem': '企业管理系统',
  'Management.businessID': '企业 ID',
  'Management.companyName': '企业名称',
  'Management.workingDays': '工作日',
  'Management.performance': '效能看板',
  'Management.board': '效能看板',
  'Management.memberList': '通讯录',
  'Management.auditLog': '操作日志',
  'Management.kitValuePack': '套件增值包',
  'Management.onlineSeatWhiteList': '在线席位白名单',
  'Management.settings': '企业设置',
  'Management.daysAndHours': '{days}天{hours}小时',
  'Management.linkEmail': '绑定邮箱',
  'Management.verify': '验证',
  'Management.2StepAuthentication': '二次身份验证',
  'Management.enterVerificationCode': '输入验证码',
  'Management.getVerificationCode': '获取验证码',
  'Management.reacquire': '{countDownNum}秒后重新获取',
  'Management.unknownError': '未知错误',
  'Management.verificationCodeError': '当前绑定邮箱距二次验证还需 {htmlString}, 请耐心等待',
  'Management.verificationCodeError2': '当前绑定邮箱距二次验证还需',
  'Management.verificationCodeError3': '请耐心等待',
  'Management.linkOrVerifyNow': '现在去绑定或验证',
  'Management.phoneVerification': '手机验证',
  'Management.authentication':
    '您还没有绑定或验证邮箱。鉴于企业文件安全的考虑，文件管控功能的使用需要绑定邮箱后，进行二次身份验证：',
  'Management.firstEmailAuthentication': '如果邮箱是首次绑定，需要7x24小时后才可进行此验证。',
  'Management.endEmailAuthentication': '绑定邮箱后，需要接收邮箱验证码来进行二次身份验证。',
  'Management.verifyPhoneNumber': '验证手机号',
  'Management.sMSVerificationCodeSent': '短信验证码已通过短信发送至',
  'Management.voiceVerificationCodeSent': '语音验证码已发送至',
  'Management.resend': '重新获取',
  'Management.getVoiceCode': '获取语音验证码',
  'Management.obtainDigitalCode': '获取数字验证码',
  'Management.minutesPleaseTry': '有效期 10 分钟。如未收到，请尝试',
  'Management.sendAgainInSeconds': '{voiceCountDownNum}秒后重新获取',
  'Management.noMobile': '您还没有绑定或验证手机号',
  'Management.linkMobile': '手机号绑定',
  'Management.mobileAuthentication':
    '鉴于企业文化安全的考虑，管理模式的使用需要绑定手机号后，接收手机验证码进行二次身份验证。',
  'Management.emailVerification': '邮箱验证',
  'Management.passwordEffect': '密码正式生效！',
  'Management.enterAdminMode': '进入管理模式',
  'Management.managementModePasswordSuccess': '管理模式密码设置成功',
  'Management.enterAdminModePassword': '请输入管理模式密码',
  'Management.enterPassword': '输入密码',
  'Management.passwordLength': '密码长度在8位以上，包含大小写字母和数字',
  'Management.newPassword': '新密码',
  'Management.inconsistentPasswords': '两次输入的密码不一致',
  'Management.pleaseEnterPassword': '请输入密码',
  'Management.pleaseEnterPasswordAgain': '请再次输入密码',
  'Management.parameterError': '参数错误',
  'Management.incorrectVerificationCode': '验证码错误',
  'Management.internalError': '内部错误，无法验证',
  'Management.exitAdminMode': '退出管理模式',
  'Management.emailOrMobile': '请选择邮箱验证或手机号验证',
  'Management.pleaseEnter': '请输入',
  'Management.passwordCannotBeEmpty': '密码不能为空',
  'Management.passwordThan8Characters': '密码不能少于 8 位字符',
  'Management.passwordCannotBeLongerThan': '密码不能超过 72 位字符',
  'Management.pwdContainNCL': '密码必须包含数字、英文大写字母和小写字母',
  'Management.pwdContainsSupported': '密码包含不支持的特殊符号',

  // 通讯录
  'Members.teamId': '企业 ID {id}',
  'Members.creator': '创建者',
  'Members.admin': '管理员',
  'Members.member': '成员',
  'Members.disabled': '禁用成员',
  'Members.pending': '待接受邀请',
  'Members.onlyOnRoot': '未分配部门成员',
  'Members.outerSider': '外部协作者',
  'Members.disableder': '已禁用成员',
  'Members.unactivated': '未激活成员',
  'Members.fileFormatError': '上传文件列名有误',
  'Members.fileFormatErrorTips': '文件列名不可被删除或修改，请检查更正后重新上传',
  'Members.fileEmptyError': '上传文件不能为空',
  'Members.tooManyUsers': '单次导入数量超过最大限制',
  'Members.tooManyUsersTips': '企业一次性导入最大限额为 {total} 条，请调整数量后重新上传',
  'Members.openFileError': '上传文件格式不正确',
  'Members.openFileErrorTips': '仅支持 xls、xlsx 格式文件，请检查后重新上传',
  'Members.partSuccess': '有成员导入失败',
  'Members.partSuccessTips': '请下载导入失败的成员信息表查看失败原因，修改后并重新上传',
  'Members.notEnoughSeat': '导入数量超过当前企业剩余席位',
  'Members.uploadError': '上传失败',
  'Members.addSubDepartment': '添加子部门',
  'Members.editDepartment': '修改部门',
  'Members.deleteDepartment': '删除部门',
  'Members.accountSettings': '账号设置',
  'Members.editNickName': '修改昵称',
  'Members.changeEmail': '换绑邮箱',
  'Members.bindEmail': '绑定邮箱',
  'Members.unbindEmail': '解绑邮箱',
  'Members.restPassword': '重置密码',
  'Members.deleteMember': '移除成员',
  'Members.cancelInvite': '取消邀请',
  'Members.setDepartment': '设置所在部门',
  'Members.checkCollaboratedFile': '查看该用户参与协作的企业文件',
  'Members.inviteJoinEnterprise': '邀请该用户加入企业',
  'Members.removeFromAllEnterpriseFiles': '将该用户从所有企业文件中移除',
  'Members.reactivateMember': '重新激活成员',
  'Members.activateMember': '激活成员',
  'Members.disableMember': '禁用成员',
  'Members.deleteLog': '删除记录',
  'Members.addMember': '添加成员',
  'Members.batchAddMember': '批量导入成员',
  'Members.inviteLink': '邀请链接',
  'Members.searchResults': '搜索结果 {results}',
  'Members.subDepartment': '子部门',
  'Members.totalNumber': '总人数',
  'Members.addMembers': '成功添加 {total} 人至 {name} 部',

  'FilePathPicker.createTitle': '选择创建位置',
  'FilePathPicker.moveTitle': '选择移动位置',
  'FilePathPicker.fileName': '文件名',
  'FilePathPicker.createIn': '创建在',
  'FilePathPicker.moveTo': '移动到',
  'FilePathPicker.move': '移动',
  'FilePathPicker.copy': '副本',
  'FilePathPicker.originalName': '原文件名',
  'FilePathPicker.selectPlaceholder': '请选择',
  'FilePathPicker.createFolder': '新建文件夹',
  'FilePathPicker.moveFailed': '移动失败',
  'FilePathPicker.noSupportTip': '当前位置不支持创建文件夹',
  'FilePathPicker.files': '文件',
  'FilePathPicker.folders': '文件夹',
  'FilePathPicker.noPermissionTip': '当前文件夹无编辑权限，无法新建{type}，请联系该文件夹管理者',
  'FilePathPicker.noMoveToTargetLocationTip': '移动失败，无移动到目标位置的权限',
  'FilePathPicker.noMoveFilePermissionTip': '无移动该文件的权限，仅文件管理者可移动',
  'FilePathPicker.targetLocationExistFile': '目标文件夹已经存在该文件',
  'FilePathPicker.createPlaceHolder': '请为在「{folderName}」中创建的文件夹命名',
  'FilePathPicker.createFolderSuccess': '新建文件夹成功',
  'FilePathPicker.createFolderFailed': '新建文件夹失败',
  'FilePathPicker.cancel': '取消',
  'FilePathPicker.confirm': '确定',
  'FilePathPicker.createCopy': '创建副本',
  'FilePathPicker.quickEntry': '快捷入口',
  'FilePathPicker.desktopSpace': '桌面与空间',
  'FilePathPicker.recent': '最近位置',
  'FilePathPicker.shared': '共享给我',
  'FilePathPicker.favorites': '我的收藏',
  'FilePathPicker.desktop': '我的桌面',
  'FilePathPicker.space': '团队空间',
  'FilePathPicker.createSuccess': '创建成功，副本保存到「{folderName}」',
  'FilePathPicker.moveSuccess': '成功移动到「{folderName}」',
  'FilePathPicker.emptyUsedTitle': '还没有移动过文件',
  'FilePathPicker.emptyUsedSubTitle': '你可以在这看到最近移动到的目的地文件夹',
  'FilePathPicker.emptySharedTitle': '还没有收到任何文件夹的共享',
  'FilePathPicker.emptySharedSubTitle': '这里可以看到被添加成为协作者的文件夹列表',
  'FilePathPicker.emptyFavoritesTitle': '暂无收藏的文件夹',
  'FilePathPicker.emptyFavoritesSubTitle': '这里可以看到你收藏的文件夹列表',
  'FilePathPicker.emptySpaceTitle': '还没有团队空间',
  'FilePathPicker.emptySpaceSubTitle': '这里可以看到有权编辑的团队空间列表',
  'FilePathPicker.emptyFolderTitle': '还未创建任何文件夹',
  'FilePathPicker.emptyFolderSubTitle': '这里可以看到「{folderName}」的文件夹列表',
  'FilePathPicker.noSearchResult': '无搜索结果',
  'FilePathPicker.searchPlaceholder': '请输入关键字',

  'TemplateReview.useTemp': '使用此模版',
  'TemplateReview.back': '返回',
  'TabContent.preview': '预览',
  'TabContent.use': '使用',
  'TabContent.testFormSubTitle': '校验答案/设置分数',
  'TabContent.tableFormSubTitle': '批量填写反馈',
  'TabContent.formSubTitle': '信息收集/问卷调查',
  'TabContent.pptSubTitle': '工作汇报/商业演讲',
  'TabContent.tableSubTitle': '任务管理/信息录入',
  'TabContent.sheetSubTitle': '工作汇报/商业演讲',
  'TabContent.docxSubTitle': '合同通知/规范书记',
  'TabContent.docSubTitle': '快速书记/文本编辑',
  'TabContent.empty': '空白',

  //企业回收站
  'Loading.loading': '加载中',
  'TrashHeader.enterpriseSetting': '企业设置',
  'TrashHeader.enterpriseTrash': '企业回收站',
  'TrashHeader.enterpriseId': '企业ID',
  'ManageEmpty.verifyTip': '为了保护企业文件安全 我们需要验证您的身份 点击',
  'ManageEmpty.toVerify': '开始验证',
  'TrashScreen.filterCriteria': '筛选条件',
  'TrashScreen.clearCriteria': '清空条件',
  'TrashScreen.filter': '筛选',
  'TrashTable.filterResult': '筛选结果',
  'TrashTable.reset': '恢复',
  'TrashTable.noData': '暂无数据',
  'RecoverFileModal.successfullyRestored': '恢复成功',
  'RecoverFileModal.restoreFiles': '恢复文件',
  'RecoverFileModal.ok': '好的',
  'RecoverFileModal.openBlank': '点击文件在新标签页中打开文件',
  'RecoverFileModal.spaceReset': '请选择 {length} 个文件所要恢复到的空间',
  'RecoverFileModal.copyAllLink': '复制所有链接',
  'RecoverFileModal.selectSpaceReset': '所选 {length} 个空间将会被恢复到团队空间下',
  'FileItem.copyLink': '复制链接',
  'DebounceSelect.noData': '没有数据',
  'FileTypeSelect.noSelectType': '未选择文件类型',
  'FileTypeSelect.fileType': '个文件类型',
  'useRecoverFile.resetSuccess': '恢复成功',
  'useRecoverFile.spaceError': '获取空间失败',
  'useScreen.searchFile': '搜索文件',
  'useScreen.searchFilePlaceholder': '输入文件标题、链接或GUID',
  'useScreen.fileType': '文件类型',
  'useScreen.searchDeleteUser': '搜索删除人',
  'useScreen.searchUserPlaceholder': '输入名称或邮箱',
  'useScreen.searchCreateUser': '搜索创建人',
  'useScreen.deleteTime': '删除时间',
  'useTable.refreshError': '未知错误，请刷新页面重试',
  'useTable.usePeriodExpired': '使用期限已过',
  'useTable.reVerify': '请重新验证！',
  'useTable.doSuccess': '操作成功',
  'useTable.modalDeleteTitle': '彻底删除文件',
  'useTable.modalDeleteContent': '此操作将彻底删除文件，不可找回，请谨慎操作！',
  'useTable.fileName': '文件名',
  'useTable.selectFileName': '文件名（已选择 {length} 个文件）',
  'useTable.creator': '创建人',
  'useTable.deleteUser': '删除人',
  'copyHandle.success': '复制成功',
  'copyHandle.error': '复制失败',
  'copyHandle.errorForCopy': '复制失败,请手动复制',
  'Time.expiredDay': '（已过期 {day} 天）',
  'Time.dayExpired': '（{day} 天后到期）',
  'Time.hour': '小时',
  'Time.mine': '分钟',
  'Time.second': '秒',

  //分享协作
  'ShareCollaboration.title': '分享协作',
  'ShareCollaboration.copySuccess': '链接已复制',
  'ShareCollaboration.copyFail': '复制失败',
  'ShareCollaboration.back': '返回',
  'ShareCollaboration.add': '添加',
  'ShareCollaboration.coauthor': '协作者',
  'ShareCollaboration.admin': '管理者',
  'ShareCollaboration.noCollaborator': '暂无',
  'ShareCollaboration.linkShare': '链接分享',
  'ShareCollaboration.shareMethod': '分享方式',
  'ShareCollaboration.qrCodeShare': '扫码分享',
  'ShareCollaboration.copyLink': '复制链接',
  'ShareCollaboration.accessPassword': '访问密码',
  'ShareCollaboration.setPermission': '设置权限',
  'ShareCollaboration.linkReadOnly': '互联网上获得链接的人只能阅读',
  'ShareCollaboration.linkInCompany': '企业内获得链接的人',
  'ShareCollaboration.readOnly': '只能阅读',
  'ShareCollaboration.comment': '可以评论',
  'ShareCollaboration.commentAndEdit': '可以评论并编辑',
  'ShareCollaboration.linkInInternet': '互联网上获得链接的人',
  'ShareCollaboration.linkInCompanyWithPassword': '企业内获取链接和密码的人',
  'ShareCollaboration.linkInternetWithPassword': '互联网上获得链接和密码的人',
  'ShareCollaboration.day': '天',
  'ShareCollaboration.searchAddCollab': '点击此处搜索并添加协作者',
  'ShareCollaboration.open': '已开启',
  'ShareCollaboration.close': '未开启,文件的协作者和管理者仍可访问',
  'ShareCollaboration.linkWithPassword': '和密码',
  'ShareCollaboration.linkPassword': '链接密码',
  'ShareCollaboration.changePassword': '更换密码',
  'ShareCollaboration.needPassword': '需要密码访问',
  'ShareCollaboration.linkExpiration': '链接有效期',
  'ShareCollaboration.switchOff': '开关关闭：链接将持续有效',
  'ShareCollaboration.switchOn': '开关打开：设置的有效期结束后，链接自动失效',
  'ShareCollaboration.expirationClose': '有效期关闭，链接永久有效',
  'ShareCollaboration.remaining': '剩余',
  'ShareCollaboration.inheritPermission': '继承权限',
  'ShareCollaboration.forbidAccess': '禁止访问',
  'ShareCollaboration.removePermission': '移除权限',
  'ShareCollaboration.modifySuccess': '修改成功',
  'ShareCollaboration.deleteSuccess': '删除成功',
  'ShareCollaboration.addCoauthor': '添加协作者',
  'ShareCollaboration.onlyManagerCanAddCoauthor': '无权限添加协作者',
  'ShareCollaboration.noPermission': '无协作权限',
  'ShareCollaboration.onlyManagerCanAddManager': '只有管理者才能添加管理者',
  'ShareCollaboration.parentCoauthor': '上级目录协作者',
  'ShareCollaboration.collapse': '收起',
  'ShareCollaboration.expand': '展开',
  'ShareCollaboration.addManager': '添加管理者',
  'ShareCollaboration.removeManager': '移除管理者权限',
  'ShareCollaboration.removeManagerSuccess': '移除部门管理者权限成功',
  'ShareCollaboration.removeManagerSuccess2': '移除人员管理者权限成功',
  'ShareCollaboration.addSuccess': '添加成功',
  'ShareCollaboration.removeSuccess': '移除管理者权限成功',
  'ShareCollaboration.setManager': '设置为管理者',
  'ShareCollaboration.addPermission': '添加权限',
  'ShareCollaboration.deleteDepartmentSuccess': '部门删除成功',
  'ShareCollaboration.deleteUserSuccess': '人员删除成功',
  'ShareCollaboration.operationSuccess': '操作成功',
  'ShareCollaboration.recent': '最近联系',
  'ShareCollaboration.organization': '组织架构',
  'ShareCollaboration.clickHereToSearchAndAdd': '点击此处搜索并添加',
  'ShareCollaboration.searchResult': '搜索结果',
  'ShareCollaboration.sendNotificationToTheOther': '添加协作者/管理者时，向对方发送通知',
  'ShareCollaboration.notSupportShare': '云文件/文件夹/团队空间不支持公开分享',
  'ShareCollaboration.noParentCollaborator': '(没有上级目录协作者)',
  'ShareCollaboration.noChildDepartment': '(无下级部门和成员)',
  'ShareCollaboration.confirmRemoveCollaborator': '确定移除管理者权限？',
  'ShareCollaboration.removeCollaborator':
    '移除管理者权限后，{name} 将变成「有编辑权限的协作者」，并出现在协作者列表里',
  'ShareCollaboration.confirmRemove': '确定移除',
  'ShareCollaboration.cancel': '取消',
  'ShareCollaboration.success': '移除成功',
  'ShareCollaboration.failed': '移除失败',
  'ShareCollaboration.noPermissionCollaboration': '你没有退出协作权限',
  'ShareCollaboration.noRoles': '暂无协作者',
  'ShareCollaboration.addRoles': '添加管理协作者',
  'ShareCollaboration.addFromContacts': '从内部联系人添加',
  'ShareCollaboration.shareByLink': '通过链接邀请',
  'ShareCollaboration.administrators': '管理者',
  'ShareCollaboration.collaborators': '协作者',
  'ShareCollaboration.administrator': '管理者',
  'ShareCollaboration.collaborator': '协作者',
  'ShareCollaboration.noCollaborators': '暂无协作者',

  // ShareCollaborationMobile
  'ShareCollaborationMobile.close': '关闭',
  'ShareCollaborationMobile.searchPlaceholder': '输入姓名/邮箱/手机/部门，搜索协作者',
  'ShareCollaborationMobile.noSearchResult': '没有找到相关成员或部门',
  'ShareCollaborationMobile.adminTitle': '管理者',
  'ShareCollaborationMobile.adminDescription': '参与文件管理的成员列表，管理者可以进行移动和删除操作。上级继承的管理者不可被移除',
  'ShareCollaborationMobile.collaboratorTitle': '协作者',
  'ShareCollaborationMobile.collaboratorDescription': '参与文件协作的成员列表，协作者不能进行移动和删除操作',
  'ShareCollaborationMobile.linkShareEnabled': '链接分享已开启',
  'ShareCollaborationMobile.linkShareDisabled': '链接分享已关闭',
  'ShareCollaborationMobile.linkShareDisabledDescription': '文件的协作者和管理员仍可访问',
  'ShareCollaborationMobile.parentDirectoryCollaborator': '上级目录协作者',
  'ShareCollaborationMobile.clickToViewCollaborators': '点击查看协作者',

  'FilePasswordInput.encryptedFileShared': '{name} 分享了一个加密文件',
  'FilePasswordInput.encryptedFilePasswordTip': '请输入密码访问文件',
  'FilePasswordInput.encryptedFilePasswordPlaceholder': '请输入文件密码',
  'FilePasswordInput.invalidGuidOrType': '未找到有效的fileGuid或fileType',
  'FilePasswordInput.confirm': '确定',
  'FilePasswordInput.PasswordRequired': '请输入密码',
  'FilePasswordInput.cannotRemoveFileManager': '不能移除此文件管理者，请从根目录进行移除',
  // 请求错误
  'Request.noAuth': '未认证，请重新登录',
  'Request.noFile': '文件不存在',
  'Request.notFound': '页面不存在',
  'Request.fileDeleted': '文件已被删除',
  'Request.noAuthorization': '没有访问权限',
  'Request.forbidden': '权限不足',
  'Request.netError': '网络异常',
  'Request.noSeats': '无可用固定席位',
  'Request.default': '请求失败，请稍后再试',
  'Request.notFoundUser': '找不到该用户',
  'Request.userEmailAlready': '该邮箱已使用！',
  'Request.notLastCertificate': '不可以解绑最后一个登录凭证',
  'Request.parmasError': '请求参数错误',
  'Request.tooManyRequests': '操作过于频繁',
  'Request.parameterError': '请求参数错误',
  'Request.noPermission': '您没有权限进行此操作',
  'Request.InvalidPassword': '密码错误',
  'Request.templateNotExist': '模板不存在',
  'Request.templateReCordExist': '模板GUID重复',
  'Request.insufficientCapacity': '目标空间或位置容量不足',

  // 企业设置
  'Enterprise.settings': '企业设置',
  'Enterprise.info': '企业信息',
  'Enterprise.logo': '企业logo',
  'Enterprise.logoTip': '暂未上传企业logo',
  'Enterprise.modify': '修改',
  'Enterprise.upload': '上传',
  'Enterprise.toSet': '去设置',
  'Enterprise.view': '查看',
  'Enterprise.name': '企业名称',
  'Enterprise.id': '企业ID',
  'Enterprise.description': '企业描述',
  'Enterprise.auth': '企业授权信息',
  'Enterprise.authDesc': '可以在企业基础信息中查看企业席位数、账号状态、有效期',
  'Enterprise.seatNum': '席位数',
  'Enterprise.seatNumDesc': '位（已使用{userCount}）',
  'Enterprise.contactBusiness': '致电商务',
  'Enterprise.accountStatus': '账号状态',
  'Enterprise.accountStatusExpired': '过期',
  'Enterprise.accountStatusValid': '有效',
  'Enterprise.validityPeriod': '有效期',
  'Enterprise.fileSettings': '文件设置',
  'Enterprise.capacityManage': '容量管理',
  'Enterprise.templateSettings': '模板库设置',
  'Enterprise.templateManage': '模版库管理',
  'Enterprise.template': '企业模板库',
  'Enterprise.safetySettings': '安全设置',
  'Enterprise.watermark': '为企业展示协作者水印',
  'Enterprise.watermarkTip': '水印构成：昵称+邮箱+公司名称',
  'Enterprise.watermarkTip2': '企业成员在登录状态下访问企业文件，将会看到带有账号信息的水印',
  'Enterprise.watermarkTip3': '目前不会对个人用户通过公开链接访问的未登录用户展示水印',
  'Enterprise.watermarkOn': '水印已开启',
  'Enterprise.watermarkOff': '水印已关闭',
  'Enterprise.recycleBin': '企业回收站',
  'Enterprise.recycleBinDesc': '查找企业所有成员因清空回收站而删除的文件，进行恢复或彻底恢复',
  'Enterprise.uploadLogoRule': '图片大小不得超过2M',
  'Enterprise.uploadLogoTip': '支持本地图片上传，支持jpg、jpeg、png、gif格式 最大尺寸限制不得超过2M',

  //企业模版库设置
  'TemplateHeader.enterpriseSetting': '企业设置',
  'TemplateHeader.enterpriseTemplateSetting': '模版库设置',
  'TemplateHeader.enterpriseId': '企业ID',
  'AddTemplatePop.createSuccess': '创建成功',
  'AddTemplatePop.createFailed': '创建失败',
  'AddTemplatePop.editSuccess': '修改成功',
  'AddTemplatePop.editFailed': '修改失败',
  'AddTemplatePop.noFileType': '没有这种类型的文件',
  'AddTemplatePop.inputSureLink': '请输入正确的链接',
  'AddTemplatePop.uploadFailed': '上传图片失败',
  'AddTemplatePop.tokenFailed': '获取上传图片token失败',
  'AddTemplatePop.limitJPG': '只能上传 JPG/PNG 格式的图片!',
  'AddTemplatePop.noUp2MB': '图片大小不能超过 2MB!',
  'AddTemplatePop.inputTemplateName': '请输入模版名称',
  'AddTemplatePop.selectType': '请选择类型',
  'AddTemplatePop.autoSelectType': '输入链接自动生成类型',
  'AddTemplatePop.inputLink': '请输入链接',
  'AddTemplatePop.uploadImgPlace': '请上传图片',
  'AddTemplatePop.templateName': '模版名称',
  'AddTemplatePop.templateType': '模版类型',
  'AddTemplatePop.templateLink': '模版链接',
  'AddTemplatePop.templateImg': '模版缩略图',
  'AddTemplatePop.img': '缩略图',
  'AddTemplatePop.uploadImg': '上传图片',
  'AddTemplatePop.uploadImgRetry': '重新上传',
  'AddTemplatePop.createTemplate': '创建模版',
  'AddTemplatePop.editTemplate': '编辑模版',
  'AddTemplatePop.updateTime': '更新时间',
  'AddTemplatePop.action': '操作',
  'AddTemplatePop.edit': '编辑',
  'AddTemplatePop.delete': '删除',
  'TemplateContent.deleteTemplate': '删除模版',
  'TemplateContent.deleteTemplateContent': '删除模板后，主站模板库中将同步被删除，是否确认删除？',
  'TemplateContent.deleteSuccess': '删除成功',
  'TemplateContent.deleteFail': '删除失败',
  'TemplateContent.create': '创建模版',
  'TemplateContent.createTip': '还没有创建任何文件，请',
  'TemplateContent.createRightNow': '立即创建',
  'TemplateContent.youCanSeeTemplate': '你可以在这里看到被自己创建的文件',
  'Image.preview': '预览',
  //操作日志
  'Operation.export': '导出日志',
  'Operation.noOperationData': '暂无操作记录',
  'Operation.search': '查询',
  'Operation.clear': '清除筛选',
  'Operation.operate': '添加操作',
  'Operation.createdAt': '时间',
  'Operation.operator': '操作者',
  'Operation.action': '操作类型',
  'Operation.operand': '操作对象',
  'Operation.relate': '相关账号',
  'Operation.ip': 'IP 地址',
  'Operation.more': '更多',
  'Operation.terminal': '操作终端',
  'Operation.viewDetail': '查看详情',
  'Operation.operateDetail': '操作详情',
  'Operation.addFilter': '添加操作筛选',
  'Operation.operateClear': '清空',
  'Operation.nape': '项',
  'Operation.selectNapeNum': '已选中 {num} 项',
  'Operation.operateSearch': '搜索',
  'Operation.noSearchResult': '未找到搜索结果',
  'Operation.selectNapeNumMax': '最多选择 {num} 项',
  'Operation.dateRange': '日期范围不可超过30日',
  'Operation.filter.time': '时间',
  'Operation.filter.operator': '操作者',
  'Operation.filter.file': '文件',
  'Operation.filter.userEmailTel': '用户名/邮箱/手机号',
  'Operation.filter.noData': '暂无数据',
  'Operation.filter.fileUrl': '文件链接地址',
  'Operation.filter.operation': '操作',
  'Operation.exportLoading': '正在导出，请稍后...',
  'Operation.exportSuccess': '导出成功',
  'Operation.exportError': '导出失败',
  'Operation.deleted': '已删除',

  // 通知 H5
  'Notification.noMsg': '无未读消息',
  'Notification.all': '全部通知',
  'Notification.unread': '未读',
  'Notification.readAll': '全部已读',
  'Notification.readonly': '只能阅读',
  'Notification.comment': '可评论',
  'Notification.edit': '可编辑',
  'Notification.apply': '申请',
  'Notification.allow': '通过',
  'Notification.processed': '已处理',
  'Notification.processedByAnother': '已被别人处理',
  'Notification.noModifyRolePermission': '没有添加协作者的权限',
  'Notification.fileNotFound': '文件不存在',
  'Notification.userNotAdmin': '用户不是管理员',
  'Notification.addTeamWorker': '添加你为协作者',
  'Notification.addAdmin': '添加你为管理者',
  'Notification.hasComment': '评论了',
  'Notification.many': '等',
  'Notification.people': '人',
  'Notification.likes': '点赞了',
  'Notification.ofComment': '的评论',
  'Notification.inYou': '你在',
  'Notification.setReminderHasExpired': '设置的提醒到期了',
  'Notification.modify': '修改',
  'Notification.delete': '删除',
  'Notification.youIn': '了你在',
  'Notification.dateReminder': '的日期提醒',
  'Notification.modifiedYourAccountInfo': '修改了你的账户信息',
  'Notification.modifiedNickName': '修改昵称为',
  'Notification.bindThisEmail': '绑定此邮箱',
  'Notification.modifiedBindEmail': '修改绑定的邮箱地址为',
  'Notification.resetPassword': '重置密码',
  'Notification.setYouAsEnterpriseAdmin': '将你设置为企业管理员',
  'Notification.modifiedEnterpriseName': '修改了企业名称',
  'Notification.mentionYou': '提到你',
  'Notification.updatedWatchArea': '更新了关注区域',
  'Notification.remindYouToViewTask': '提醒你查看任务',
};
