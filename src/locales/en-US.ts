export default {
  'hooks.Authorization.loginFailedMessage': 'Login failed',
  'service.Me.anonymousName': 'Anonymous',
  'Common.loadingText': 'System is processing, please wait...',
  'Common.confirm': 'Confirm',
  'Common.cancel': 'Cancel',
  'File.file': 'file',
  'File.document': 'document',
  'File.newdoc': 'Documents',
  'File.modoc': 'Classic Docs',
  'File.mosheet': 'Sheets',
  'File.presentation': 'Presentation',
  'File.table': 'Tables',
  'File.form': 'Forms',
  'File.normalForm': 'Normal Form',
  'File.tableViewForm': 'Table View Form',
  'File.quizForm': 'Quiz Form',
  'File.folder': 'Folder',
  'Login.loginTitle': 'Login',
  'Login.autoLanguageSetting': 'Auto',
  'Login.autoThemeSetting': 'Auto',
  'Login.lightThemeSetting': 'lightMode',
  'Login.darkThemeSetting': 'darkMode',
  'LoginView.userNameInputPlaceholder': 'Enter your account',
  'LoginView.passwordInputPlaceholder': 'Enter your password',
  'LoginView.loginButtonText': 'Login',
  'LoginView.userNameInputLabel': 'Email',
  'LoginView.passwordInputLabel': 'password',
  'LoginView.emailFormatInaccurate': 'The email format is inaccurate',
  'MessageCenter.onlyreadButtonText': 'Look read',
  'MessageCenter.onlyUnreadButtonText': 'Look unread',
  'MessageCenter.allMarkReadButtonText': 'All marks are not read',
  'UserCenter.settings': 'Settings',
  'UserCenter.myBusiness': 'My business',
  'UserCenter.switchLanguages': 'Switch languages',
  'UserCenter.logOut': 'Log out',
  'UserCenter.myDesktopCapacit': 'My desktop capacity',
  'UserCenter.totalEnterpriseCapacity': 'Total enterprise capacity',
  'SearchCenter.me': 'Me',
  'SearchCenter.update': 'Update',
  'SearchCenter.open': 'Open',
  'SearchCenter.searchFile': 'Search file',
  'SearchCenter.noData': 'No search results',
  'SearchCenter.used': 'Recently used',
  'SearchCenter.search': 'Search results',
  'Header.backButtonTipText': 'Back',
  'Header.backToButtonTipText': 'Back to',
  'Header.createButtonTipText': 'Create',
  'Header.inputPlaceholder': 'Untitled',
  'Header.teamButtonText': 'Collaborate',
  'Header.shareButtonText': 'Share',
  'Header.fileMenuButtonTipText': 'File menu',
  'Header.historyButtonText': 'History',
  'Header.demoButtonText': 'Presentation',
  'Header.editButtonText': 'Edit',
  'Header.downloadButtonText': 'Download',
  'Header.sharingCollaborationButtonText': 'SharingCollaboration',

  'Editor.saveStatus.offlineSaving': 'Saving offline content',
  'Editor.saveStatus.offlinePersistSucceed': 'Edits saved offline, will sync when connected',
  'Editor.saveStatus.offline': 'No network connection, content will be saved offline',
  'Editor.saveStatus.offlinePersistFailed': 'Offline save failed',
  'Editor.saveStatus.online': 'Content will auto-save',
  'Editor.saveStatus.onlineSaving': 'Saving',
  'Editor.saveStatus.saveAccepted': 'Saving',
  'Editor.saveStatus.saveSucceed': 'Auto-save successfully',
  'Editor.saveStatus.applySucceed': 'Content auto-updated',
  'Editor.saveStatus.saveFailed': 'Save failed',
  'Editor.saveStatus.applyFailed': 'Save failed',
  'Editor.saveStatus.saveTimeout': 'Save failed',
  'Editor.saveStatus.acceptTimeout': 'Save failed',
  'Editor.syncStatus.syncSaving': 'Syncing offline data, please wait...',
  'Editor.syncStatus.syncSucceed': 'Offline data synced, content will auto-save',
  'Editor.syncStatus.syncFailed': 'Save failed',
  'Editor.noSupport': 'This feature is not currently supported',
  'Editor.ok': 'Ok',
  'Editor.syncSaving': 'Syncing offline data, please wait...',
  'Editor.syncSucceed': 'Offline data synced, content will auto-save',
  'Editor.syncFailed': 'Offline data sync failed',
  'Editor.noEditing': 'System has prohibited editing',
  'Editor.noEditingContent': 'You do not have permission to edit this document, please contact the file manager',
  'Editor.sorry': 'Sorry...',
  'Editor.fileDeleted': 'The file has been deleted',
  'Editor.saveFailed': 'Save failed',
  'Editor.saveFailedContent': 'Save failed, please copy the current editing data, refresh and continue to use',

  'Header.favorite': 'Favorite',
  'Header.unfavorite': 'Unfavorite',
  'Header.favorited': 'Favorited',
  'Header.unfavorited': 'Unfavorited',
  'BackToPopover.searchFiles': 'Search files...',
  'BackToPopover.backTo': 'Back to',
  'BackToPopover.myDesktop': 'My Desktop',
  'BackToPopover.workbench': 'Workbench',
  'BackToPopover.recentlyUsed': 'Recently Used',
  'BackToPopover.quickAccess': 'Quick Access',
  'AvatarGroup.restCount': '{count} more collaborators',

  'SiderMenu.siderMenuCreactText': 'create',
  'SiderMenu.createDisablePopText':
    'The current folder does not have editing permissions and cannot create a new folder. Please contact the folder manager',
  'SiderMenu.new': 'new',
  'SiderMenu.siderMenuRecentText': 'Recent Files',
  'SiderMenu.siderMenuShareText': 'Share with me',
  'SiderMenu.siderMenuFavoritesText': 'My Collection',
  'SiderMenu.siderMenuDesktopText': 'Desktop',
  'SiderMenu.siderMenuSpaceText': 'Space',
  'SiderMenu.siderMenuTrashText': 'Trash',
  'SiderMenu.siderMenuBusinessText': 'Business',
  'SiderMenu.siderMenuCreateDocText': 'Doc',
  'SiderMenu.siderMenuCreateMoDocText': 'MoDoc',
  'SiderMenu.siderMenuCreateTableText': 'Sheet',
  'SiderMenu.siderMenuCreateMoTableText': 'Table',
  'SiderMenu.siderMenuCreatePptText': 'PPT',
  'SiderMenu.siderMenuCreateFormText': 'Form',
  'SiderMenu.siderMenuCreateOrdinaryFormText': 'Sheet',
  'SiderMenu.siderMenuCreateTableFormText': 'Table sheet',
  'SiderMenu.siderMenuCreateTestFormText': 'Test sheet',
  'SiderMenu.siderMenuCreateFolderText': 'Folder',
  'SiderMenu.siderMenuCreateSpace': 'Space',
  'SiderMenu.siderMenuCreateBlank': 'Blank',
  'SiderMenu.siderMenuCreateThink': 'Mind map',
  'SiderMenu.siderMenuCreateCloud': 'Cloud file',
  'SiderMenu.siderMenuCreateSpaceText': 'Space',
  'SiderMenu.siderMenuCreateUploadFolderText': 'Upload folder',
  'SiderMenu.siderMenuCreateUploadFileText': 'Upload file',
  'SiderMenu.siderMenuCreateFileUploadText': 'File upload',
  'SiderMenu.siderMenuCreateTemplateText': 'Create from Template Library',
  'SiderMenu.siderMenuTemplateCreateText': 'Template Library create',
  'SiderMenu.cancel': 'cancel',
  'SiderMenu.confirm': 'confirm',
  'SiderMenu.inputName': 'Input name',

  'CreateFileMenu.networkStatusTipText': 'Please check your network',
  'CreateFolder.folderTitle': 'New folder',
  'CreateFolder.saveVersionTitle': 'Save version',
  'CreateFolder.spaceTitle': 'Create a new team space',
  'CreateFolder.folderPlaceholder': 'Please enter the folder name',
  'CreateFolder.spacePlaceholder': 'Please enter team space name',
  'CreateFolder.ruleMessage': 'Please provide complete information',
  'CreateFolder.saveVersionPlaceholder': 'Please enter save version name',
  'Editor.openInNewTab': 'Open in new tab',
  'AddNewPopover.templateLibrary': 'Template',
  'AddNewPopover.upload': 'Upload',
  'AddNewPopover.uploadFolder': 'Upload Folders',

  'Error.loginGuideTitle': 'Please log in to access',
  'Error.loginText': 'Login',
  'Error.notFound': 'The page does not exist',
  'Error.notFoundDes': 'The page you visited does not exist',
  'Error.goBackHomePage': 'Back to desktop',
  'Error.fileDeleteTitle': 'Error',
  'Error.fileDeleteSubTitle': 'The file you are trying to access has been deleted',
  'Error.switchAccount': 'Switch account',
  'Error.accessRestrictedTitle': 'Login required to access',
  'Error.noSeatsTitle': 'Have no fixed seats available',
  'Error.noSeatsTitleDes':
    'You have not yet obtained a seat for this package. Please contact the administrator to allocate or purchase a seat.',
  'Error.contactAdmin': 'Contact the administrator',
  'Error.purchaseSeats': 'Purchase seats',
  'Error.unknownErrorTitle': 'The page did not load successfully',
  'Error.unknownErrorSubTitle': 'Please make sure that the network is good and refresh the page to try again.',
  'Error.netErr': 'Network error',
  'Error.netErrDes': 'Please check the network and access again',
  'Error.refresh': 'Refresh',
  'Error.noLogin': "You haven't logged in",
  'Error.noLoginDes':
    'The document owner has set permissions for specific users to access. Please log in and try again',
  'Error.noPermission': 'No permission to access',
  'Error.noPermissionDes':
    'The document owner did not add you for collaboration. Please contact the owner to grant access permission',
  'Error.noPermissionDesH5': 'The currently logged-in account {user} does not have permission to access this document',
  'Error.applyPermissionSent': 'Application for permission has been sent to the administrator',
  'Error.applyPermissionSentSuccess': 'Your application has been sent successfully',
  'Error.applyForPermission': 'Apply for access permission',
  'Error.selectPermissionType': 'Please select the type of permission to apply for',
  'Error.addRemarkPlaceholder': 'Add remarks here if needed',
  'Error.sendApplication': 'Send application',
  'Error.permissionReadable': 'Readable',
  'Error.permissionCommentable': 'Commentable',
  'Error.permissionEditable': 'Editable',
  'Error.applyForPermissionBtn': 'Apply for permission',
  'Error.applyTooFrequent': 'Application sent too frequently, please try again in two minutes',

  // 用户被禁用页面
  'Forbidden.title': 'The account has been deactivated',
  'Forbidden.tips':
    '{email},Your business account has been deactivated, and if you need to log in, please contact your business administrator to restore your account.',

  'deleteConfirm.title': 'Confirm the deletion',
  'deleteConfirm.content':
    'Confirm the deletion of files? Once deleted, all collaborators will lose access to the file.',
  'deleteConfirm.cancel': 'Cancel',
  'deleteConfirm.success': 'The deletion is successful',
  'deleteConfirm.error': 'Deletion failed',
  'useFileDetail.creator': 'Creator',
  'useFileDetail.modocTitle': 'Document Information',
  'useFileDetail.mosheetTitle': 'Table Information',
  'useFileDetail.tableTitle': 'Application form information',
  'useFileDetail.pptTitle': 'Slide information',
  'useFileDetail.formTitle': 'Form Information',
  'useFileDetail.statisticWordCount': 'Total word count',
  'useFileDetail.charCount': 'Number of characters (including spaces)',
  'useFileDetail.charCountWithoutSpaces': 'Character count (including spaces)',
  'useFileDetail.page': 'Number of pages',
  'useFileDetail.total': 'Total number of words',
  'useFileDetail.paragraphCount': 'Number of paragraphs',
  'useFileDetail.sections': 'Number of sections',
  'useFileDetail.views': 'Number of reads',
  'useFileDetail.times': 'times',
  'useFileDetail.imgTitle': 'Image title',
  'RenameModal.edit': 'Edit',
  'RenameModal.editSuccess': 'Edit success',
  'RenameModal.editError': 'Edit error',
  'RenameModal.title': 'File rename',
  'RenameModal.InputPlaceholder': 'Please enter a name for the file',
  'RenameModal.validatorMessage': 'The file name must not contain the following illegal characters',
  'formatTime.justNow': '1 minute',
  'formatTime.minutesAgo': '{minutes} minutes ago',
  'formatTime.today': 'Today, {hhmm}',
  'formatTime.yesterday': 'Yesterday, {hhmm}',
  'File.setFilter': 'Set up filtering',
  'File.clearTrash': 'Empty the recycle bin',
  'File.clearTrashSuccess': 'The recycle bin was emptied successfully',
  'File.clearTrashError': 'Failed to empty the recycle bin',
  'File.clearTrashWarn':
    'Whether you confirm that the recycle bin is emptied, confirm that it will not be able to be retrieved, please operate with caution!',
  'File.clearTrashTips':
    'Tips: The files in the recycle bin are also digital assets of the enterprise, and the enterprise has the right to recycle',
  'File.resetFirst': 'The file has been restored to its original location',
  'File.resetError': 'Recovery failed',
  'File.checkedTotal': '{checked} items selected files',
  'File.fileName': 'file name',
  'File.createName': 'Created by',
  'File.updateTime': 'Last update',
  'File.updatedAt': 'Updated',
  'File.openTime': 'Open time',
  'File.editTime': 'Edit Time',
  'File.createdAt': 'Creation time',
  'File.fileSize': 'Size',
  'File.uncheck': 'Uncheck',
  'File.allCheck': 'Select all',
  'File.move': 'Move',
  'File.delete': 'Delete',
  'File.more': 'More',
  'File.deleteSuccess': 'The deletion is successful',
  'File.deleteError': 'Deletion failed',
  'File.deleteTips': 'The maximum limit for bulk deleting files is {max}',
  'File.newTabOpens': 'A new tab opens',
  'File.edit': 'Edit',
  'File.star': 'Add to my favorites',
  'File.starSuccess': 'Added to my favorites successfully',
  'File.starError': 'Failed to add to my favorites',
  'File.removeStar': 'Removed from my favorites',
  'File.removeSuccess': 'Removed from my favorites successfully',
  'File.removeError': 'Failed to remove from my favorites',
  'File.share': 'Share',
  'File.view': 'View',
  'File.shareInfo': 'Sharing Information',
  'File.collaboration': 'Collaboration',
  'File.download': 'Download',
  'File.downloadSuccess': 'The download was successful',
  'File.downloadError': 'The download failed',
  'File.png': 'Image',
  'File.reName': 'Rename',
  'File.moveTo': 'Move to',
  'File.copyTo': 'Create a copy to',
  'File.clearRecord': 'Clear the record',
  'File.clearFilter': 'Clear the filter',
  'File.recentlyOpened': 'Recently opened',
  'File.recentlyEdit': 'Recently edit',
  'File.deleteTime': 'Deletion time',
  'File.recover': 'Recover files',
  'File.deleteCompletely': 'Delete completely',
  'File.noShareTitle': 'I haven not received any invitations to collaborate yet',
  'File.noShareDescription': 'You can see the files and folders where you have been added as a writer here.',
  'File.noFavoritesTitle': 'No favorites yet',
  'File.noFavoritesDescription': 'You can see your favorite files and folders here.',
  'File.noDesktopTitle': 'No files have been created yet.',
  'File.noDesktopDescription': 'You can see the files you created here.',
  'File.noTrashTitle': 'There are still no garbage and files.',
  'File.noTrashDescription':
    'You can see the deleted files and folders here, and the files and folders here can be restored to their original location.',

  //文件列表 h5
  'File.defaultSort': 'Default sort',
  'File.sortByFileName': 'Sort by file name',
  'File.sortByCreationTime': 'Sort by creation time',
  'File.byNewnessSortBy': 'By newness sort by',
  'File.noRecentlyUsedFiles': 'No recently used files',
  'File.folderAtTop': 'Folder at top',

  'MessageCenter.commented': 'commented on you',
  'MessageCenter.mentioned': 'You were mentioned',
  'MessageCenter.addCollaborator': 'Add you as a collaborator {invitedRole}',
  'MessageCenter.setAdministrator': 'Set you up as an enterprise administrator',
  'MessageCenter.inviteJoinBusiness': 'Invite you to join the business',
  'MessageCenter.newMembersJoin': 'New members join',
  'MessageCenter.deleteDocument': 'Delete the document',
  'MessageCenter.remindsReviewTasks': 'Reminds you to review tasks',
  'MessageCenter.liked': 'Liked',
  'MessageCenter.NotificationToDoChanges': 'Removed you in',
  'MessageCenter.dateArrived': 'The date has arrived',
  'MessageCenter.mentionYou': 'Mention you',
  'MessageCenter.moveYouOfBusiness': 'Move you out of the business',
  'MessageCenter.handingOverBusinessToYou': 'Handing over the business to you',
  'MessageCenter.companyNameChanged': 'The company name has been changed"{name}"',
  'MessageCenter.openedBusinessLink': 'The comment opened the business invitation link',
  'MessageCenter.closedBusinessLink': 'The business invitation link is turned off',
  'MessageCenter.taskReminders': 'Reminds you to review tasks',
  'MessageCenter.tableSelectionReminders': 'Table selection reminders',
  'MessageCenter.changeUserConfig': 'Changing the ID (Email, Nickname, Password) of a Member',
  'MessageCenter.systemNotifications': 'System notifications',
  'MessageCenter.application': 'Application',
  'MessageCenter.markRead': 'Mark as read',
  'MessageCenter.join': 'Join',
  'MessageCenter.discussion': 'Discussion',
  'MessageCenter.permissions': 'Permissions',
  'File.downloadErr': 'File download failed. Please try again later.',
  'File.startDownload': 'Download started',
  'File.startImport': 'File import started',
  'File.isLoading': 'The file is loading, please wait...',
  'File.importSuccess': 'File imported successfully',
  'File.importErr': 'File import failed',
  'File.importTypeErr': 'File type import error',
  'File.isLastImg': "It's already the last one",
  'File.noData': 'No data available',
  'File.unknownParentDirectory': 'Unknown superior directory / ',
  'File.noPermissionToViewParentDirectory': 'Have no permission to view the superior directory',

  'MessageCenter.basicInformationModification': 'Basic information modification',
  'Management.emailOrMobile': 'Please choose email or mobile verification',
  //团队空间
  'Space.countSpace': 'spaces',
  'Space.createSpace': 'New space',
  'Space.createTeamSpace': 'Create a new team space',
  'Space.sure': 'Sure',
  'Space.cancel': 'Cancel',
  'Space.enterSpaceName': 'please enter space name',
  'Space.SpaceNameHeader': 'Space name',
  'Space.infoSuccess': 'Created successfully',
  'Space.infoEmpty': 'The space name cannot be empty',
  'Space.infoWaring1': 'There should be no Spaces at the beginning. They have been automatically removed',
  'Space.infoWaring2': 'The space name cannot exceed 20 characters',
  'Space.rightClickShare': 'Share',
  'Space.rightClickCollaboration': 'Collaboration',
  'Space.rightClickSetting': 'Setting',
  'Space.rightClickDelete': 'Delete',
  'Space.infoEditSuccess': 'edit successfully',
  'Space.teamspaceSetting': 'Team Space Setting',
  'Space.teamspaceOwnership': 'Ownership',
  'Space.teamspaceWhatOwnership': 'What is ownership?',
  'Space.teamspaceBelongsto': 'The ownership of this space belongs to',
  'Space.teamspaceOwnershipExp':
    'Space Ownership determines who manages the files in the team space. If the ownership belongs to the enterprise, it will be affected by the enterprise is security Settings, be included in the audit system, and be incorporated into the statistics of the performance dashboard.',
  'Space.teamspaceConfirmDeletion': 'Confirm deletion',
  'Space.teamspaceDeleteSuccess': 'Deletion successful',
  'Space.teamspaceDeleteTipText':
    'Have you confirmed the deletion of the team space? After deletion, all collaborators will be unable to access this team space',

  //个人设置
  'Profile.title': 'Personal Settings',
  'Profile.accountInfo': 'Account information',
  'Profile.preferenceSitting': 'Preference Settings',
  'Profile.accountID': 'Account ID',
  'Profile.modifyInfo': 'Modify the basic information',
  'Profile.safetySetting': 'Safety Settings',
  'Profile.accountPd': 'Password',
  'Profile.modify': 'Modify',
  'Profile.modifyImg': 'Modify the profile picture',
  'Profile.uploadImg': 'Upload the avatar',
  'Profile.nickName': 'Nickname',
  'Profile.enterNickname': 'Please enter the nickname',
  'Profile.nickNameRule': 'The nickname must not exceed 20 characters at most',
  'Profile.modifySuccess': 'The modification of user information was successful',
  'Profile.modifyFailed': 'Failed to modify the user information',
  'Profile.getUploadToken': 'Failed to obtain the uploaded image token',
  'Profile.uploadImgSuccess': 'The picture was uploaded successfully',
  'Profile.uploadImgFailed': 'Failed to upload the picture',
  'Profile.changePd': 'Change the password',
  'Profile.forgetPd': 'Forgot the password',
  'Profile.currentPassword': 'Current password',
  'Profile.newPassword': 'New password',
  'Profile.confirmNewPassword': 'Confirm the new password',
  'Profile.currentPasswordRequired': 'The current password must be filled in',
  'Profile.currentPasswordPlaceholder': 'Please enter the current password',
  'Profile.newPasswordPlaceholder': 'Please enter the new password',
  'Profile.newPasswordRequired': 'Enter a new password',
  'Profile.newPasswordLength8': 'The password must be no less than 8 characters',
  'Profile.newPasswordRule': 'The password must contain numbers, uppercase and lowercase English letters',
  'Profile.confirmNewPasswordPlaceholder': 'Please enter the new password again',
  'Profile.confirmNewPasswordRequired': 'Please confirm the new password',
  'Profile.confirmNewPasswordMatch': 'The passwords entered twice are inconsistent',
  'Profile.changePdSuccess': 'The password was modified successfully.',
  'Profile.changePdFailed': 'The password you entered is incorrect',
  'Profile.uploadImgRuleType': 'Only images in JPG, PNG, GIF and JPEG formats can be uploaded',
  'Profile.uploadImgRuleSize': 'The size of the picture must not exceed 2MB',
  'Profile.networkError': 'Abnormal network response',
  'ManagementSiderMenu.backDesktopTest': 'Return to the home page',
  'FileMenuPopover.favorite': 'Favorite',
  'FileMenuPopover.move': 'Move',
  'FileMenuPopover.createCopy': 'Create Copy',
  'FileMenuPopover.download': 'Download',
  'FileMenuPopover.print': 'Print',
  'FileMenuPopover.saveVersion': 'Save Version',
  'FileMenuPopover.viewHistory': 'View History',
  'FileMenuPopover.viewCommentList': 'View Comments',
  'FileMenuPopover.help': 'Help',
  'FileMenuPopover.docGuide': 'Document Guide',
  'FileMenuPopover.docShortcut': 'Document Shortcuts',
  'FileMenuPopover.mosheetGuide': 'Sheet Guide',
  'FileMenuPopover.mosheetShortcut': 'Sheet Shortcuts',
  'FileMenuPopover.delete': 'Delete',
  'FileMenuPopover.downImage': 'Image',
  'FileMenuPopover.downWord': 'Word',
  'FileMenuPopover.downPDF': 'PDF',
  'FileMenuPopover.downMarkdown': 'Markdown',
  'FileMenuPopover.downWPS': 'WPS',
  'FileMenuPopover.downImagePDF': 'Image PDF',
  'FileMenuPopover.downExcel': 'Excel',
  'FileMenuPopover.downZip': 'ZIP',
  'FileMenuPopover.downPPTX': 'PPTX',
  'FileMenuPopover.convertToMoSheet': 'Convert to ShiMoSheet',
  'FileMenuPopover.downToExcel': 'Download as Excel',
  'FileMenuPopover.tableHelp': 'Table Help Center',
  'FileMenuPopover.addComment': 'Add Comment',
  'FileMenuPopover.viewComment': 'View Comment',
  'FileMenuPopover.formHelp': 'Form Help Center',
  'FileMenuPopover.documentInfo': 'Document Information',
  'FileMenuPopover.fileInfo': 'File Information',
  'FileMenuPopover.noMovePermissionTip':
    'You do not have permission to move files, please contact the file manager or enterprise administrator',
  'FileMenuPopover.noCreateCopyPermissionTip':
    'You do not have permission to create copies, please contact the file manager or enterprise administrator',

  'UploadBoard.uploadSuccessTitle': 'Upload completed',
  'UploadBoard.uploadingTitle': 'uploading',
  'UploadBoard.uploadFailTitle': 'Error occurred',
  'UploadBoard.uploadCancelTitle': 'items have been cancelled',
  'UploadBoard.uploadConfirmCancelTitle': 'Are you sure to cancel the upload?',
  'UploadBoard.uploadConfirmCancelContent':
    'Closing the panel will cancel the file being uploaded, and files that have been successfully uploaded will not be affected. Are you sure you want to cancel the upload?',
  'UploadBoard.uploadConfirmCancelOkText': 'Cancel Upload',
  'UploadBoard.uploadConfirmCancelCancelText': 'Continue uploading',
  'UploadBoard.uploadFailTipTitleText':
    'Partial file upload failed, please re upload or copy the following error message and submit it to customer service for processing:',
  'UploadBoard.uploadFailMessageText': 'error message',
  'UploadBoard.uploadCopyFailMessageText': 'Copy error messages',
  'UploadBoard.uploadCopySuccessText': 'Replicating Success',
  'UploadBoard.uploadCheckFailMessageText': 'View error messages',
  'UploadBoard.uploadRetryText': 'retry',
  'UploadBoard.uploadOpenFolderText': 'Open the folder where you are located',
  'UploadBoard.uploadStatusTipCancelText': 'Cancelled',
  'UploadBoard.uploadExpTipRetractText': 'Retract',
  'UploadBoard.uploadExpTipExpandText': 'Expand',
  'UploadBoard.uploadExpTipRetractErrorMessageText': 'Hide error messages',
  'UploadBoard.failTipNum': '{number}errors',
  'UploadBoard.failTipTitle':
    'Unknown error detected, please upload or copy the error information separately for the following {number} files and submit them to customer service for processing:',
  'UseFileUpload.networkError': 'Network anomaly',
  'UseFileUpload.noSpaceTitle': 'Insufficient space and memory',
  'UseFileUpload.noSpaceContent':
    'Your space memory is insufficient. Please contact customer service or sales to purchase additional space capacity;',
  'UseFileUpload.noSpaceOkText': 'Sure',

  // 企业管理
  'Management.backDesktop': 'Return to the main site',
  'Management.enterpriseManagementSystem': 'Enterprise management system',
  'Management.companyName': 'Company name',
  'Management.workingDays': 'Working days',
  'Management.performance': 'Performance board',
  'Management.board': 'Performance board',
  'Management.memberList': 'Member list',
  'Management.auditLog': 'Audit log',
  'Management.kitValuePack': 'Kit Value Pack',
  'Management.onlineSeatWhiteList': 'Online seat whitelist',
  'Management.settings': 'Settings',
  'Management.template': 'Template settings',
  'Management.daysAndHours': '{days} days and {hours} hours',
  'Management.linkEmail': 'Link email',
  'Management.verify': 'verify',
  'Management.2StepAuthentication': '2-Step Authentication',
  'Management.enterVerificationCode': 'Enter verification code',
  'Management.getVerificationCode': 'Get verification code',
  'Management.reacquire': 'Send again in {countDownNum} seconds',
  'Management.unknownError': 'Unknown error',
  'Management.verificationCodeError':
    'Currently linked email still needs {remains} for 2-step authentication. Please wait.',
  'Management.verificationCodeError2': 'Currently linked email still needs',
  'Management.verificationCodeError3': 'for 2-step authentication. Please wait.',
  'Management.linkOrVerifyNow': 'Link or verify now',
  'Management.phoneVerification': 'Phone verification',
  'Management.authentication':
    'You have not linked or verified your email address. For information security, the File Management feature can only be used after you have successfully linked your email address, and completed 2-step authentication: ',
  'Management.firstEmailAuthentication':
    'If the email is verified for the first time, it takes 7x24 hours for the second authentication.',
  'Management.endEmailAuthentication':
    'After the verification of the email, you will receive the email Captcha to perform the second authentication.',
  'Management.verifyPhoneNumber': 'Verify phone number',
  'Management.sMSVerificationCodeSent': 'The SMS verification code has been sent via SMS to',
  'Management.voiceVerificationCodeSent': 'The voice verification code has been sent to',
  'Management.resend': 'Resend',
  'Management.getVoiceCode': 'Get a voice verification code',
  'Management.obtainDigitalCode': 'Obtain a digital verification code',
  'Management.minutesPleaseTry': 'valid for 10 minutes. If not received, please try',
  'Management.sendAgainInSeconds': 'Send again in {voiceCountDownNum} seconds',
  'Management.noMobile': 'You have not linked or verified your mobile number',
  'Management.linkMobile': 'Link mobile number',
  'Management.mobileAuthentication':
    'For the purpose of company security, you will need to do 2-factor authentication before entering the admin supervise mode.',
  'Management.emailVerification': 'E-mail verification',
  'Management.passwordEffect': 'Password officially takes effect!',
  'Management.enterAdminMode': 'Enter Admin Mode',
  'Management.managementModePasswordSuccess': 'Management mode password successfully set',
  'Management.enterAdminModePassword': 'Please enter the Admin Mode password',
  'Management.enterPassword': 'Enter password',
  'Management.passwordLength':
    'Password length is 8 characters or more, including uppercase and lowercase letters and numbers',
  'Management.newPassword': 'New password',
  'Management.inconsistentPasswords': 'Inconsistent passwords',
  'Management.pleaseEnterPassword': 'Please enter password',
  'Management.pleaseEnterPasswordAgain': 'Please enter the password again',
  'Management.parameterError': 'Parameter error',
  'Management.incorrectVerificationCode': 'Incorrect verification code',
  'Management.internalError': 'Internal error, unable to verify',
  'Management.exitAdminMode': 'Exit admin mode',
  'Management.pleaseEnter': 'Please enter',
  'Management.passwordCannotBeEmpty': 'Password cannot be empty',
  'Management.passwordThan8Characters': 'The password must not beLess than 8 characters',
  'Management.passwordCannotBeLongerThan': 'Password cannot exceed 72 characters',
  'Management.pwdContainNCL': 'Password must contain numbers, capitalLetters and lowercase letters',
  'Management.pwdContainsSupported': 'Password contains special symbols that are not supported',

  // 通讯录
  'Members.teamId': 'Team ID {id}',
  'Members.creator': 'Creator',
  'Members.admin': 'Admin',
  'Members.member': 'Member',
  'Members.disabled': 'Disable members',
  'Members.pending': 'Pending invitation',
  'Members.onlyOnRoot': 'No department member is assigned',
  'Members.outerSider': 'External collaborators',
  'Members.disableder': 'Members are disabled',
  'Members.unactivated': 'Unactivated',
  'Members.fileFormatError': 'The uploaded file is incorrectly listed',
  'Members.fileFormatErrorTips':
    'The file column name cannot be deleted or modified, please check the corrections and upload it again',
  'Members.fileEmptyError': 'The uploaded file cannot be empty',
  'Members.tooManyUsers': 'The number of imports at a time exceeds the maximum limit',
  'Members.tooManyUsersTips':
    'The maximum number of one-time imports is {total}, so adjust the number and upload them again',
  'Members.openFileError': 'The upload file format is incorrect',
  'Members.openFileErrorTips': 'Only xls and xlsx files are supported, please check and upload them again',
  'Members.partSuccess': 'There are members that fail to be imported',
  'Members.partSuccessTips':
    'Download the member information table for import failures to view the reason for the failure, modify it, and upload it again',
  'Members.notEnoughSeat': 'The number of imported seats exceeds the remaining seats of the current enterprise',
  'Members.uploadError': 'Upload failed',
  'Members.addSubDepartment': 'Add subDepartment',
  'Members.editDepartment': 'Edit department',
  'Members.deleteDepartment': 'Delete department',
  'Members.accountSettings': 'Account settings',
  'Members.editNickName': 'Change your nickname',
  'Members.changeEmail': 'Change your email address',
  'Members.bindEmail': 'Bind an email address',
  'Members.unbindEmail': 'Unbind an email address',
  'Members.restPassword': 'Reset your password',
  'Members.deleteMember': 'Remove members',
  'Members.cancelInvite': 'Cancel the invitation',
  'Members.setDepartment': 'Set your department',
  'Members.checkCollaboratedFile': 'View the enterprise files that the user is collaborating on',
  'Members.inviteJoinEnterprise': 'Invite the user to join the enterprise',
  'Members.removeFromAllEnterpriseFiles': 'Remove the user from all enterprise files',
  'Members.reactivateMember': 'Reactivate the member',
  'Members.activateMember': 'Activate the member',
  'Members.disableMember': 'Disable members',
  'Members.deleteLog': 'Delete the record',
  'Members.addMember': 'Add member',
  'Members.batchAddMember': 'Batch add member',
  'Members.inviteLink': 'Invite link',
  'Members.searchResults': 'Search results {results}',
  'Members.subDepartment': 'Sub-department',
  'Members.totalNumber': 'Total number',
  'Members.addMembers': 'Successfully added {total} people to {name} department',

  'FilePathPicker.createTitle': 'Select directory',
  'FilePathPicker.moveTitle': 'Select Move Location',
  'FilePathPicker.fileName': 'Filename',
  'FilePathPicker.createIn': 'Create In',
  'FilePathPicker.moveTo': 'Move To',
  'FilePathPicker.move': 'Move',
  'FilePathPicker.copy': 'Copy',
  'FilePathPicker.originalName': 'Original file name',
  'FilePathPicker.selectPlaceholder': 'Please Select',
  'FilePathPicker.createFolder': 'New Folder',
  'FilePathPicker.moveFailed': 'Move failed',
  'FilePathPicker.noMoveToTargetLocationTip': 'Move failed, no permission to move to the target location',
  'FilePathPicker.noMoveFilePermissionTip': 'No permission to move the file, only the file manager can move',
  'FilePathPicker.targetLocationExistFile': 'The target folder already has this file',
  'FilePathPicker.noSupportTip': 'Current location does not support creating folders',
  'FilePathPicker.files': 'files',
  'FilePathPicker.folders': 'folders',
  'FilePathPicker.noPermissionTip':
    'The current folder has no editing permissions and cannot create new {type}. Please contact the folder manager.',
  'FilePathPicker.createPlaceHolder': 'Please name the folder to be created in "{folderName}"',
  'FilePathPicker.createFolderSuccess': 'New folder created successfully',
  'FilePathPicker.createFolderFailed': 'Failed to create new folder',
  'FilePathPicker.cancel': 'Cancel',
  'FilePathPicker.confirm': 'Confirm',
  'FilePathPicker.createCopy': 'Create Copy',
  'FilePathPicker.quickEntry': 'Quick Access',
  'FilePathPicker.desktopSpace': 'Desktop & Team Spaces',
  'FilePathPicker.recent': 'Recent',
  'FilePathPicker.shared': 'Shared with Me',
  'FilePathPicker.favorites': 'My Favorites',
  'FilePathPicker.desktop': 'My Desktop',
  'FilePathPicker.space': 'Team Space',
  'FilePathPicker.createSuccess': 'Created successfully, copy saved to 「{folderName}」',
  'FilePathPicker.moveSuccess': 'Successfully moved to 「{folderName}」',
  'FilePathPicker.emptyUsedTitle': 'No files have been moved yet',
  'FilePathPicker.emptyUsedSubTitle': 'You can see the destination folder of the recently moved files here',
  'FilePathPicker.emptySharedTitle': 'No shared folders yet',
  'FilePathPicker.emptySharedSubTitle': 'You can see the list of folders added as collaborators here',
  'FilePathPicker.emptyFavoritesTitle': 'No favorite folders yet',
  'FilePathPicker.emptyFavoritesSubTitle': 'You can see the list of your favorite folders here',
  'FilePathPicker.emptySpaceTitle': 'No team spaces yet',
  'FilePathPicker.emptySpaceSubTitle': 'You can see the list of team spaces you can edit here',
  'FilePathPicker.emptyFolderTitle': 'No folders created yet',
  'FilePathPicker.emptyFolderSubTitle': 'You can see the list of folders in「{folderName}」here',
  'FilePathPicker.noSearchResult': 'No search results',
  'FilePathPicker.searchPlaceholder': 'Please enter a keyword',

  'TemplateReview.useTemp': 'Use this template',
  'TemplateReview.back': 'Return',
  'TabContent.preview': 'Preview',
  'TabContent.use': 'Use',
  'TabContent.testFormSubTitle': 'Verify answers/set scores',
  'TabContent.tableFormSubTitle': 'Batch fill in feedback',
  'TabContent.formSubTitle': 'Information collection/questionnaire survey',
  'TabContent.pptSubTitle': 'Work Report/Business Speech',
  'TabContent.tableSubTitle': 'Task management/information entry',
  'TabContent.sheetSubTitle': 'Work Report/Business Speech',
  'TabContent.docxSubTitle': 'Contract Notice/Standard Secretary',
  'TabContent.docSubTitle': 'Quick Secretary/Text Editing',
  'TabContent.empty': 'Blank ',

  //企业回收站
  'Loading.loading': 'Loading...',
  'TrashHeader.enterpriseSetting': 'Enterprise Setting',
  'TrashHeader.enterpriseTrash': 'Enterprise Trash',
  'TrashHeader.enterpriseId': 'Enterprise ID',
  'ManageEmpty.verifyTip':
    'In order to protect the security of enterprise files, we need to verify your identity by clicking',
  'ManageEmpty.toVerify': 'Begin verification',
  'TrashScreen.filterCriteria': 'Filter criteria',
  'TrashScreen.clearCriteria': 'Clear criteria',
  'TrashScreen.filter': 'Filter',
  'TrashTable.filterResult': 'Filter result',
  'TrashTable.reset': 'Reset',
  'TrashTable.noData': 'No data',
  'RecoverFileModal.successfullyRestored': 'Recovered successfully',
  'RecoverFileModal.restoreFiles': 'restore files',
  'RecoverFileModal.ok': 'ok',
  'RecoverFileModal.openBlank': 'Click on the file to open it in a new tab',
  'RecoverFileModal.spaceReset': 'Please select the space to recover {length} files',
  'RecoverFileModal.copyAllLink': 'Copy all links',
  'RecoverFileModal.selectSpaceReset': 'The selected {length} spaces will be restored to the team space',
  'FileItem.copyLink': 'Copy link',
  'DebounceSelect.noData': 'No data',
  'FileTypeSelect.noSelectType': 'No file type selected',
  'FileTypeSelect.fileType': 'file types',
  'useRecoverFile.resetSuccess': 'Recovery was successful',
  'useRecoverFile.spaceError': 'Failed to obtain space',
  'useScreen.searchFile': 'search for file',
  'useScreen.searchFilePlaceholder': 'Enter file title, link, or UID',
  'useScreen.fileType': 'File type',
  'useScreen.searchDeleteUser': 'Search and delete people',
  'useScreen.searchUserPlaceholder': 'Enter name or email address',
  'useScreen.searchCreateUser': 'Search for Creator',
  'useScreen.deleteTime': 'Delete Time',
  'useTable.refreshError': 'Unknown error, please refresh the page and try again',
  'useTable.usePeriodExpired': 'The usage period has expired',
  'useTable.reVerify': 'Please revalidate!',
  'useTable.doSuccess': 'Operation successful',
  'useTable.modalDeleteTitle': 'Completely delete files',
  'useTable.modalDeleteContent':
    'This operation will completely delete the file and cannot be retrieved. Please proceed with caution!',
  'useTable.fileName': 'File name',
  'useTable.selectFileName': 'File Name ({length} files selected)',
  'useTable.creator': 'Founder',
  'useTable.deleteUser': 'Delete person',
  'copyHandle.success': 'Replicating Success',
  'copyHandle.error': 'Copy failed',
  'copyHandle.errorForCopy': 'Copy failed, please manually copy',
  'Time.expiredDay': '(Expired {day} days)',
  'Time.dayExpired': '(Due in {day} days)',
  'Time.hour': 'hour',
  'Time.mine': 'minute',
  'Time.second': 'second',

  //分享协作
  'ShareCollaboration.title': 'Sharing and Collaboration',
  'ShareCollaboration.copySuccess': 'The link has been copied.',
  'ShareCollaboration.copyFail': 'Copy Failed',
  'ShareCollaboration.back': 'Back',
  'ShareCollaboration.add': 'Add',
  'ShareCollaboration.coauthor': 'Coauthor',
  'ShareCollaboration.admin': 'Administrator',
  'ShareCollaboration.noCollaborator': 'Not yet.',
  'ShareCollaboration.linkShare': 'Link Sharing',
  'ShareCollaboration.shareMethod': 'Sharing Method',
  'ShareCollaboration.qrCodeShare': 'Scan the code to share',
  'ShareCollaboration.copyLink': 'Copy Link',
  'ShareCollaboration.accessPassword': 'Access Password',
  'ShareCollaboration.setPermission': 'Set Permissions',
  'ShareCollaboration.linkReadOnly': 'People on the internet with the link can only read',
  'ShareCollaboration.linkInCompany': 'People within the company with the link',
  'ShareCollaboration.readOnly': 'Read Only',
  'ShareCollaboration.comment': 'Can Comment',
  'ShareCollaboration.commentAndEdit': 'Can Comment and Edit',
  'ShareCollaboration.linkInInternet': 'People on the internet with the link',
  'ShareCollaboration.linkInCompanyWithPassword': 'People within the company with the link and password',
  'ShareCollaboration.linkInternetWithPassword': 'People on the internet who obtain both the link and the password',
  'ShareCollaboration.day': 'Day(s)',
  'ShareCollaboration.searchAddCollab': 'Click here to search and add collaborators',
  'ShareCollaboration.open': 'Enabled',
  'ShareCollaboration.close': 'Not enabled: Collaborators and administrators still have access',
  'ShareCollaboration.linkWithPassword': 'and Password',
  'ShareCollaboration.linkPassword': 'Link Password',
  'ShareCollaboration.changePassword': 'Change Password',
  'ShareCollaboration.needPassword': 'Requires password to access',
  'ShareCollaboration.linkExpiration': 'Link Expiration',
  'ShareCollaboration.switchOff': 'Switch Off: The link will remain active indefinitely',
  'ShareCollaboration.switchOn': 'Switch On: After the set expiration date, the link will become invalid',
  'ShareCollaboration.expirationClose': 'Expiration is disabled, the link is permanent',
  'ShareCollaboration.remaining': 'Remaining',
  'ShareCollaboration.inheritPermission': 'Inherit Permissions',
  'ShareCollaboration.forbidAccess': 'Access Forbidden',
  'ShareCollaboration.removePermission': 'Remove Permissions',
  'ShareCollaboration.modifySuccess': 'Modification Successful',
  'ShareCollaboration.deleteSuccess': 'Deletion Successful',
  'ShareCollaboration.addCoauthor': 'Add Coauthor',
  'ShareCollaboration.onlyManagerCanAddCoauthor': 'There is no permission to add collaborators',
  'ShareCollaboration.noPermission': 'No collaboration permission',
  'ShareCollaboration.onlyManagerCanAddManager': 'Only administrators can add administrators',
  'ShareCollaboration.parentCoauthor': 'Parent Folder Collaborators',
  'ShareCollaboration.collapse': 'Collapse',
  'ShareCollaboration.expand': 'Expand',
  'ShareCollaboration.addManager': 'Add Administrator',
  'ShareCollaboration.removeManager': 'Remove Administrator Rights',
  'ShareCollaboration.removeManagerSuccess': 'Department Administrator Rights Removed Successfully',
  'ShareCollaboration.removeManagerSuccess2': 'User Administrator Rights Removed Successfully',
  'ShareCollaboration.addSuccess': 'Added Successfully',
  'ShareCollaboration.removeSuccess': 'Administrator Rights Removed Successfully',
  'ShareCollaboration.setManager': 'Set as Administrator',
  'ShareCollaboration.addPermission': 'Add Permission',
  'ShareCollaboration.deleteDepartmentSuccess': 'Department Deleted Successfully',
  'ShareCollaboration.deleteUserSuccess': 'User Deleted Successfully',
  'ShareCollaboration.operationSuccess': 'Operation Successful',
  'ShareCollaboration.recent': 'Recent contact',
  'ShareCollaboration.organization': 'Organization Structure',
  'ShareCollaboration.clickHereToSearchAndAdd': 'Click here to search and add',
  'ShareCollaboration.searchResult': 'Search Results',
  'ShareCollaboration.sendNotificationToTheOther': 'Send notification when adding collaborators or administrators',
  'ShareCollaboration.notSupportShare': 'Cloud files/folders/team Spaces do not support public sharing',
  'ShareCollaboration.noParentCollaborator': '(Collaborators without a superior directory)',
  'ShareCollaboration.noChildDepartment': '(There are no subordinate departments or members)',
  'ShareCollaboration.confirmRemoveCollaborator': 'Are you sure to remove the manager permission?',
  'ShareCollaboration.removeCollaborator':
    'After removing the manager permission, {name} will become a "collaborator with edit access" and appear in the collaborator list',
  'ShareCollaboration.confirmRemove': 'Confirm Removal',
  'ShareCollaboration.cancel': 'Cancel',
  'ShareCollaboration.success': 'Successfully removed',
  'ShareCollaboration.failed': 'Failed to remove',
  'ShareCollaboration.noPermissionCollaboration': "You haven't exited the collaboration permission",
  'ShareCollaboration.noRoles': 'No collaborators yet',
  'ShareCollaboration.addRoles': 'Add Collaborators/Administrators',
  'ShareCollaboration.addFromContacts': 'Add from Internal Contacts',
  'ShareCollaboration.shareByLink': 'Invite via Link',
  'ShareCollaboration.administrators': 'Administrators',
  'ShareCollaboration.collaborators': 'Collaborators',
  'ShareCollaboration.administrator': 'Administrator',
  'ShareCollaboration.collaborator': 'Collaborator',
  'ShareCollaboration.noCollaborators': 'No collaborators yet',

  'FilePasswordInput.encryptedFileShared': '{name} shared an encrypted file',
  'FilePasswordInput.encryptedFilePasswordTip': 'Please enter the password to access the file',
  'FilePasswordInput.encryptedFilePasswordPlaceholder': 'Please enter the file password',
  'FilePasswordInput.invalidGuidOrType': 'No valid fileGuid or fileType found',
  'FilePasswordInput.confirm': 'Confirm',
  'FilePasswordInput.PasswordRequired': 'Password is required',
  'FilePasswordInput.cannotRemoveFileManager':
    'This file manager cannot be removed. Please remove it from the root directory',

  // 请求错误
  'Request.noAuth': 'Not authenticated, please login again',
  'Request.noFile': 'File not found',
  'Request.notFound': 'Page not found',
  'Request.fileDeleted': 'The file has been deleted',
  'Request.noAuthorization': 'Access denied',
  'Request.forbidden': 'Forbidden',
  'Request.netError': 'Network error',
  'Request.noSeats': 'No available fixed seats',
  'Request.default': 'Request failed, please try again later',
  'Request.userEmailAlready': 'The mailbox is already in use!',
  'Request.notFoundUser': 'This user could not be found',
  'Request.notLastCertificate': 'You cannot unbind the last login credential',
  'Request.parmasError': 'The request parameter is incorrect',
  'Request.tooManyRequests': 'Too many requests, please try again later',
  'Request.parameterError': 'Parameter error',
  'Request.noPermission': 'No permission to access',
  'Request.InvalidPassword': 'Invalid password',
  'Request.templateNotExist': 'Template not exist',
  'Request.templateReCordExist': 'Template GUID already exists',
  'Request.insufficientCapacity': 'Insufficient target space or location capacity',

  // 企业设置
  'Enterprise.settings': 'Enterprise Settings',
  'Enterprise.info': 'Enterprise Information',
  'Enterprise.logo': 'Enterprise Logo',
  'Enterprise.logoTip': 'No enterprise logo uploaded yet',
  'Enterprise.modify': 'Modify',
  'Enterprise.upload': 'Upload',
  'Enterprise.toSet': 'Go to set up',
  'Enterprise.view': 'View',
  'Enterprise.name': 'Enterprise Name',
  'Enterprise.id': 'Enterprise ID',
  'Enterprise.description': 'Enterprise Description',
  'Enterprise.auth': 'Enterprise Authorization Information',
  'Enterprise.authDesc':
    'Enterprise seat number, account status, and validity period can be viewed in the enterprise basic information',
  'Enterprise.seatNum': 'Number of Seats',
  'Enterprise.seatNumDesc': 'seats (used {userCount})',
  'Enterprise.contactBusiness': 'Contact Sales',
  'Enterprise.accountStatus': 'Account Status',
  'Enterprise.accountStatusExpired': 'Expired',
  'Enterprise.accountStatusValid': 'Valid',
  'Enterprise.validityPeriod': 'Validity Period',
  'Enterprise.fileSettings': 'File Settings',
  'Enterprise.capacityManage': 'Capacity Management',
  'Enterprise.templateSettings': 'Template Library Settings',
  'Enterprise.templateManage': 'Template Library Management',
  'Enterprise.template': 'Enterprise Template Library',
  'Enterprise.safetySettings': 'Security Settings',
  'Enterprise.watermark': 'Display collaborator watermarks for enterprise',
  'Enterprise.watermarkTip': 'Watermark composition: Nickname + Email + Company Name',
  'Enterprise.watermarkTip2':
    'Enterprise members will see watermarks with account information when accessing enterprise files while logged in',
  'Enterprise.watermarkTip3':
    'Currently, watermarks are not displayed for individual users accessing through public links without logging in',
  'Enterprise.watermarkOn': 'Watermark on',
  'Enterprise.watermarkOff': 'Watermark off',
  'Enterprise.recycleBin': 'Enterprise Recycle Bin',
  'Enterprise.recycleBinDesc':
    'Find files deleted by all enterprise members due to emptying the recycle bin, and restore or permanently restore them',
  'Enterprise.uploadLogoRule': 'The size of the picture must not exceed 2MB',
  'Enterprise.uploadLogoTip':
    'Supports local image upload, supports jpg, jpeg, png, gif format, maximum size limit of 2M',

  //企业模版库设置
  'TemplateHeader.enterpriseSetting': 'Enterprise setting',
  'TemplateHeader.enterpriseTemplateSetting': 'Template library settings',
  'TemplateHeader.enterpriseId': 'enterpriseId',
  'AddTemplatePop.createSuccess': 'Create success',
  'AddTemplatePop.createFailed': 'Create failed',
  'AddTemplatePop.editSuccess': 'Edit success',
  'AddTemplatePop.editFailed': 'Edit failed',
  'AddTemplatePop.noFileType': 'No file type',
  'AddTemplatePop.inputSureLink': 'Please enter the correct link',
  'AddTemplatePop.uploadFailed': 'Image upload failed',
  'AddTemplatePop.tokenFailed': 'Failed to obtain the uploaded image token',
  'AddTemplatePop.limitJPG': 'Only JPG/PNG format images can be uploaded!',
  'AddTemplatePop.noUp2MB': 'The image size cannot exceed 2MB!',
  'AddTemplatePop.inputTemplateName': 'Please enter the template name',
  'AddTemplatePop.selectType': 'Please select the template type',
  'AddTemplatePop.autoSelectType': 'Input link to automatically generate type',
  'AddTemplatePop.inputLink': 'please enter the link',
  'AddTemplatePop.uploadImgPlace': 'Please upload the template image',
  'AddTemplatePop.templateName': 'Template name',
  'AddTemplatePop.templateType': 'Template type',
  'AddTemplatePop.templateLink': 'Template link',
  'AddTemplatePop.templateImg': 'Template image',
  'AddTemplatePop.img': 'Image',
  'AddTemplatePop.uploadImg': 'Upload image',
  'AddTemplatePop.uploadImgRetry': 'Re-upload',
  'AddTemplatePop.createTemplate': 'Create template',
  'AddTemplatePop.editTemplate': 'Edit template',
  'AddTemplatePop.updateTime': 'Update time',
  'AddTemplatePop.action': 'Action',
  'AddTemplatePop.edit': 'Edit',
  'AddTemplatePop.delete': 'Delete',
  'TemplateContent.deleteTemplate': 'Delete template',
  'TemplateContent.deleteTemplateContent':
    'After deleting the template, it will be synchronized and deleted from the main template library. Are you sure you want to confirm the deletion?',
  'TemplateContent.deleteSuccess': 'Delete success',
  'TemplateContent.deleteFail': 'Delete fail',
  'TemplateContent.create': 'Create template',
  'TemplateContent.createTip': 'No files have been created yet, please',
  'TemplateContent.createRightNow': 'Create now',
  'TemplateContent.youCanSeeTemplate': 'You can see the files created by yourself here',
  'Image.preview': 'Preview',
  //操作日志
  'Operation.export': 'Export logs',
  'Operation.noOperationData': 'There are no operation records for the time being.',
  'Operation.search': 'Search',
  'Operation.clear': 'Clear',
  'Operation.operate': 'Add operate',
  'Operation.createdAt': 'Time',
  'Operation.operator': 'Operator',
  'Operation.action': 'Operation type',
  'Operation.operand': 'Operand',
  'Operation.relate': 'Related accounts',
  'Operation.ip': 'IP address',
  'Operation.more': 'More',
  'Operation.terminal': 'Operation terminal',
  'Operation.viewDetail': 'View details',
  'Operation.operateDetail': 'Operation details',
  'Operation.addFilter': 'Add operation filtering',
  'Operation.operateClear': 'Clear',
  'Operation.nape': 'Nape',
  'Operation.selectNapeNum': 'The {num} item has been selected.',
  'Operation.operateSearch': 'Search',
  'Operation.noSearchResult': 'No search results were found.',
  'Operation.selectNapeNumMax': 'You can select a maximum of {num} items',
  'Operation.dateRange': 'The date range cannot exceed 30 days.',
  'Operation.filter.time': 'Time',
  'Operation.filter.operator': 'Operator',
  'Operation.filter.file': 'File',
  'Operation.filter.userEmailTel': 'Username / Email / Mobile Phone Number',
  'Operation.filter.noData': 'No data',
  'Operation.filter.fileUrl': 'File link address',
  'Operation.filter.operation': 'Operation',
  'Operation.exportLoading': 'Exporting in progress, please wait...',
  'Operation.exportSuccess': 'Export successful',
  'Operation.exportError': 'Export failed',
  'Operation.deleted': 'have deleted',

  // 通知 H5
  'Notification.noMsg': 'No messages',
  'Notification.all': 'All notifications',
  'Notification.unread': 'Unread',
  'Notification.readAll': 'All read',
  'Notification.readonly': 'Only read',
  'Notification.comment': 'Can comment',
  'Notification.edit': 'Can edit',
  'Notification.apply': 'Apply',
  'Notification.allow': 'Allow',
  'Notification.processed': 'Processed',
  'Notification.processedByAnother': 'Processed by another',
  'Notification.noModifyRolePermission': 'No permission to add collaborators',
  'Notification.fileNotFound': 'File not found',
  'Notification.userNotAdmin': 'User is not an administrator',
  'Notification.addTeamWorker': 'Add you as a collaborator',
  'Notification.addAdmin': 'Add you as a administrator',
  'Notification.hasComment': 'Commented',
  'Notification.many': 'and',
  'Notification.people': 'people',
  'Notification.likes': 'liked',
  'Notification.ofComment': 'of comment',
  'Notification.inYou': 'You are in',
  'Notification.setReminderHasExpired': 'The reminder you set has expired',
  'Notification.modify': 'Modify',
  'Notification.delete': 'Delete',
  'Notification.youIn': 'in',
  'Notification.dateReminder': 'date reminder',
  'Notification.modifiedYourAccountInfo': 'Modified your account information',
  'Notification.modifiedNickName': 'Modified nickname to',
  'Notification.bindThisEmail': 'Bind this email',
  'Notification.modifiedBindEmail': 'Modified bind email address to',
  'Notification.resetPassword': 'Reset password',
  'Notification.setYouAsEnterpriseAdmin': 'Set you as an enterprise administrator',
  'Notification.modifiedEnterpriseName': 'Modified enterprise name to',
  'Notification.mentionYou': 'Mentioned you',
  'Notification.updatedWatchArea': 'Updated watch area',
  'Notification.remindYouToViewTask': 'Remind you to view task',
};
