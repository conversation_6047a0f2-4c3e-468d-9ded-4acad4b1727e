import { ActionSheet } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';

import { usePermissions } from '@/components/Collaboration/hooks/usePermission';
import type { CollaborationData } from '@/components/Collaboration/types';
import { getCollaborationDataInfo } from '@/components/Collaboration/utils';

import styles from './CollaborationShareMobile.less';
import { ShareAction } from './components/ShareAction';
import { ShareUserList } from './components/ShareUserList';
import { CopyLink } from './components/SimpleButton';
import type { ShareInfo } from './store/CollaboratorManagement';
import { useCollaborationStore } from './store/CollaboratorManagement';

interface CollaborationShareMobileProps {
  guid: string;
  visible: boolean;
  close: () => void;
  toggle: () => void;
}

export function CollaborationShareMobile({ guid, visible, toggle, close }: CollaborationShareMobileProps) {
  const [data, setData] = useState<CollaborationData>();
  const { setCollaborationData, setGuid, setShare } = useCollaborationStore((state) => state);
  const { roleCheckable } = usePermissions(data);

  const actions = useMemo(() => {
    const premissionAction = [
      {
        key: 'userList',
        text: <ShareUserList guid={guid} />,
      },
      {
        key: 'shareAction',
        text: <ShareAction data={data} guid={guid} toggleParentVisible={toggle} />,
      },
    ];

    const premission = roleCheckable ? premissionAction : [];

    return [
      ...premission,
      {
        key: 'copyLink',
        text: <CopyLink onClick={close} />,
      },
    ];
  }, [guid, data, toggle, roleCheckable, close]);

  useEffect(() => {
    if (guid) {
      setGuid(guid);
      getCollaborationDataInfo(guid).then((res) => {
        if (res) {
          setData(res.data);
          setCollaborationData(res.data);
          setShare({
            copyUrl: res.copyUrl,
          } as ShareInfo);
        }
      });
    }
    return () => {
      setData(undefined);
    };
  }, [guid, setCollaborationData, setGuid, setShare]);

  return (
    <ActionSheet
      actions={actions}
      cancelText="关闭"
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={close}
    />
  );
}
