import { Checkbox, Tabs } from 'antd-mobile';

import { ReactComponent as CheckboxIcon } from '@/assets/images/svg/checkbox.svg';
import { ReactComponent as CheckedIcon } from '@/assets/images/svg/checked-box.svg';

import { useRecentUserRole } from '../hooks/useUserRole';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { CollarboratorCard } from './Collaborator';
import styles from './CollaboratorManagement.less';
import { CollaboratorStruct } from './CollaboratorStruct';
import { CollaboratorDeptUerItem } from './CollaboratorUserItem';
import { SearchPopup } from './SearchPopup';

export function AddCollaborator() {
  const { data } = useRecentUserRole();
  const { sendNotification, setSendNotification } = useCollaborationStore((state) => state);

  return (
    <>
      {/* 搜索框 */}
      <SearchPopup />

      <Tabs className={styles.addCollaboratorTabs}>
        <Tabs.Tab key="recently" title="最近联系人">
          {data.length > 0 && (
            <CollarboratorCard>
              {data.map((item) => (
                <CollaboratorDeptUerItem key={item.id} node={item} />
              ))}
            </CollarboratorCard>
          )}
        </Tabs.Tab>
        <Tabs.Tab key="organization" title="组织架构">
          <CollaboratorStruct />
        </Tabs.Tab>
      </Tabs>
      <div className={styles.addCollaboratorFooter}>
        <Checkbox
          checked={sendNotification}
          icon={(checked) => (checked ? <CheckedIcon /> : <CheckboxIcon />)}
          onChange={setSendNotification}
        >
          <span className={styles.addCollaboratorFooterText}> 添加协作者时，向对方发送通知</span>
        </Checkbox>
      </div>
    </>
  );
}
