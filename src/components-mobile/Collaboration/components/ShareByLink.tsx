import { ActionSheet } from 'antd-mobile';
import { useCallback, useMemo } from 'react';

import { CloseList } from '@/components/Collaboration/components/SelectOptions';
import type { CollaborationData } from '@/components/Collaboration/types';
import { handleUpdateShareStatus } from '@/components/Collaboration/utils';

import styles from '../CollaborationShareMobile.less';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { LinkPermissionSettings, ShareByLinkExpirySettings, ShareByLinkSharingSwitch } from './ShareByLinkActions';
import { CopyLink } from './SimpleButton';

interface IProps {
  visible: boolean;
  onClose: () => void;
  open: boolean;
  setOpen: (open: boolean) => void;
  guid: string;
}

export function ShareByLink({ visible, guid, onClose, open, setOpen }: IProps) {
  const { setCollaborationData, collaborationData } = useCollaborationStore((state) => state);

  const handleShare = useCallback(
    (status: boolean) => {
      const shareMode = status ? CloseList[0].value : 'private';
      handleUpdateShareStatus(guid, shareMode);
      setOpen(status);
      setCollaborationData({ ...(collaborationData as CollaborationData), shareMode });
    },
    [collaborationData, guid, setCollaborationData, setOpen],
  );

  const actions = useMemo(() => {
    return [
      {
        key: 'linkSharingSwitch',
        text: <ShareByLinkSharingSwitch checked={open} onChange={handleShare} />,
      },
      ...(open
        ? [
            {
              key: 'linkExpirySettings',
              text: <ShareByLinkExpirySettings />,
            },
            {
              key: 'linkPermissionSettings',
              text: <LinkPermissionSettings />,
            },
          ]
        : []),
      {
        key: 'copyLink',
        text: <CopyLink disabled={!open} onClick={close} />,
      },
    ];
  }, [handleShare, open]);

  return (
    <ActionSheet
      actions={actions}
      cancelText="关闭"
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={onClose}
    />
  );
}
