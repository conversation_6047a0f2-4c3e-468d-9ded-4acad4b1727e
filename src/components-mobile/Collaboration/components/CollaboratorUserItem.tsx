import toast from 'antd-mobile/es/components/toast';
import { memo, useCallback, useMemo, useState } from 'react';

import { catchApiResult } from '@/api/Request';
import { ReactComponent as CareDownIcon } from '@/assets/images/svg/care-down.svg';
import { ReactComponent as CareRightIcon } from '@/assets/images/svg/care-right.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as SuperiorOrganization } from '@/assets/images/svg/superiorOrganization.svg';
import { ReactComponent as TopLevel } from '@/assets/images/svg/topLevel.svg';
import { useDisclosure } from '@/hooks/useDisclosure';

import { useRefresh } from '../hooks/useRefresh';
import type { TreeNode } from '../hooks/useUserRole';
import { FrontRole } from '../hooks/useUserRole';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { setDefaultPermission } from '../utils/mobile';
import type { CloseProp } from './AddPermissionSettings';
import { AddPermissionSettings } from './AddPermissionSettings';
import {
  CollarboratorCardAvatar,
  CollarboratorCardContent,
  CollarboratorCardLeft,
  CollarboratorCardName,
} from './Collaborator';
import { ItemButton } from './CollaboratorItem';
import styles from './CollaboratorStruct.less';
import { MobileItemAddRole, MobileItemsPrenet } from './const';

// 添加权限按钮
export const AddPermissionButton = memo(({ node, onClick }: { node: TreeNode; onClick: (data: CloseProp) => void }) => {
  const { guid, sendNotification } = useCollaborationStore((state) => state);

  async function handleAddCollaborator() {
    const [err] = await catchApiResult(setDefaultPermission(guid, node, sendNotification));
    if (err) {
      toast.show({ icon: 'fail', content: err.data.msg });
      return;
    }
    toast.show({ icon: 'success', content: '添加成功' });
    onClick({ type: FrontRole.collaborator, name: 'editor' });
  }

  return <ItemButton displayText={'添加权限'} icon={false} showBtn={node.showBtn} onClick={handleAddCollaborator} />;
});

export const EditAdminButton = memo(({ node, onClick }: { node: TreeNode; onClick: (data: CloseProp) => void }) => {
  const { isOpen, open, close } = useDisclosure(false);
  const { handleRefresh } = useRefresh();

  const data = useMemo(
    () => ({
      avatar: node.avatar,
      name: node.name,
      role: node.role!,
      id: node.id,
      departmentId: node.type === 'department' ? node.id : undefined,
      email: node.email,
    }),
    [node],
  );

  function handleClose(data?: CloseProp) {
    if (data) {
      const { type, name } = data;
      close();
      const newType = name === 'remove' ? FrontRole.none : type;
      onClick({
        type: newType,
        name,
      });
      handleRefresh();
    } else {
      close();
    }
  }

  return (
    <>
      <ItemButton icon displayText={node.displayText} showBtn={node.showBtn} onClick={open} />
      <AddPermissionSettings close={handleClose} data={data} type={FrontRole.admin} visible={isOpen} />
    </>
  );
});

// 编辑权限按钮
export const EditPermissionButton = memo(
  ({ node, onClick }: { node: TreeNode; onClick: (data: CloseProp) => void }) => {
    const { isOpen, open, close } = useDisclosure(false);
    const { handleRefresh } = useRefresh();

    const data = useMemo(
      () => ({
        avatar: node.avatar,
        name: node.name,
        role: node.role!,
        id: node.id,
        departmentId: node.type === 'department' ? node.id : undefined,
        email: node.email,
      }),
      [node],
    );

    function handleClose(info?: CloseProp) {
      if (info) {
        const { type, name } = info;
        close();
        // 删除全部权限
        if (name === 'remove') {
          onClick({
            type: FrontRole.none,
            name,
          });
          handleRefresh();
          return;
        } else if (name === 'merger') {
          // 删除管理者权限
          onClick({
            type: FrontRole.admin,
            name,
          });
          handleRefresh();
          return;
        } else if (name === 'none') {
          handleRefresh();
        }
        onClick({
          type,
          name,
        });
      } else {
        close();
      }
    }

    return (
      <>
        <ItemButton icon displayText={node.displayText} showBtn={node.showBtn} onClick={open} />
        <AddPermissionSettings close={handleClose} data={data} type={FrontRole.collaborator} visible={isOpen} />
      </>
    );
  },
);

// 编辑权限按钮
export const ParentButton = memo(({ node, onClick }: { node: TreeNode; onClick: (data: CloseProp) => void }) => {
  const { isOpen, open, close } = useDisclosure(false);

  const data = useMemo(
    () => ({
      avatar: <SuperiorOrganization />,
      name: node.name,
      role: node.role!,
      id: node.id,
      departmentId: node.type === 'department' ? node.id : undefined,
      email: node.email,
    }),
    [node],
  );

  function handleClose(info?: CloseProp) {
    if (info) {
      const { type, name } = info;
      close();
      onClick({
        type,
        name,
      });
    } else {
      close();
    }
  }

  return (
    <>
      <ItemButton icon displayText={node.displayText} showBtn={node.showBtn} onClick={open} />
      <AddPermissionSettings close={handleClose} data={data} type={FrontRole.parent} visible={isOpen} />
    </>
  );
});

export const PermissionButton = memo(({ defaultNode }: { defaultNode: TreeNode }) => {
  const [role, setRole] = useState(defaultNode.frontRole);
  const [node, setNode] = useState(defaultNode);

  function handleClick(data: CloseProp) {
    setRole(data.type);
    setNode({
      ...node,
      role: data.name,
      displayText: [...MobileItemAddRole(), ...MobileItemsPrenet()].find((item) => item.key === data.name)!.label,
    });
  }

  return (
    <>
      {role === FrontRole.none && <AddPermissionButton node={node} onClick={handleClick} />}
      {role === FrontRole.admin && <EditAdminButton node={node} onClick={handleClick} />}
      {role === FrontRole.collaborator && <EditPermissionButton node={node} onClick={handleClick} />}
      {role === FrontRole.parent && <ParentButton node={node} onClick={handleClick} />}
    </>
  );
});

export const CollaboratorUserItem = memo(({ node }: { node: TreeNode }) => {
  return (
    <CollarboratorCardContent>
      <CollarboratorCardLeft>
        <div
          style={{
            paddingLeft: `${node.level * 10 + 23}px`,
            display: 'flex',
            alignItems: 'center',
            overflow: 'hidden',
          }}
        >
          <CollarboratorCardAvatar avatar={node.avatar || ''} />
          <CollarboratorCardName email={node.email} name={node.name} />
        </div>
      </CollarboratorCardLeft>
      <PermissionButton defaultNode={node} />
    </CollarboratorCardContent>
  );
});

export const CollaboratorDeptTreeItem = memo(
  ({
    node,
    loadDepartmentData,
    defaultChildrenData = [],
    defaultExpanded = false,
  }: {
    node: TreeNode;
    loadDepartmentData: (
      departmentId: number,
      level: number,
    ) => Promise<{
      childrenData: TreeNode[];
    }>;
    defaultChildrenData?: TreeNode[];
    defaultExpanded?: boolean;
  }) => {
    const [isExpanded, setIsExpanded] = useState(defaultExpanded);
    const [children, setChildren] = useState<TreeNode[]>(defaultChildrenData);
    const [isLoading, setIsLoading] = useState(false);

    const handleToggle = useCallback(async () => {
      if (!isExpanded && children.length === 0) {
        // 首次展开，需要加载数据
        setIsLoading(true);
        try {
          const { childrenData } = await loadDepartmentData(node.id, node.level + 1);
          setChildren(childrenData);
        } catch (error) {
          console.error('Failed to load department data:', error);
        } finally {
          setIsLoading(false);
        }
      }
      setIsExpanded(!isExpanded);
    }, [children.length, isExpanded, loadDepartmentData, node.id, node.level]);

    return (
      <div>
        <CollarboratorCardContent className={styles.content}>
          <CollarboratorCardLeft>
            <div
              style={{
                paddingLeft: `${node.level * 10}px`,
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
              }}
              onClick={handleToggle}
            >
              <span className={styles.expandIcon}>
                {isLoading ? '...' : isExpanded ? <CareDownIcon /> : <CareRightIcon />}
              </span>
              {node.id === 1 ? <TopLevel height={32} width={32} /> : <Organization height={32} width={32} />}
              <CollarboratorCardName className={styles.departmentName} name={node.name} />
            </div>
          </CollarboratorCardLeft>
          <PermissionButton defaultNode={node} />
        </CollarboratorCardContent>

        {/* 渲染子节点 */}
        {isExpanded && (
          <div>
            {children.map((child) => {
              if (child.type === 'department') {
                return <CollaboratorDeptTreeItem key={child.id} loadDepartmentData={loadDepartmentData} node={child} />;
              } else {
                return <CollaboratorUserItem key={child.id} node={child} />;
              }
            })}
          </div>
        )}
      </div>
    );
  },
);

// 组织、人员(自动刷新)
export const CollaboratorDeptUerItem = memo(({ node }: { node: TreeNode }) => {
  return (
    <CollarboratorCardContent>
      <CollarboratorCardLeft>
        {node.type === 'department' ? (
          <Organization height={32} width={32} />
        ) : (
          <CollarboratorCardAvatar avatar={node.avatar || ''} />
        )}
        <CollarboratorCardName email={node.email} name={node.name} />
      </CollarboratorCardLeft>
      <PermissionButton defaultNode={node} />
    </CollarboratorCardContent>
  );
});
