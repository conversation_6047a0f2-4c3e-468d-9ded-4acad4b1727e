import React, { isValidElement, useCallback, useMemo } from 'react';

import type { Role } from '@/api/Collaboration.type';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as SuperiorOrganization } from '@/assets/images/svg/superiorOrganization.svg';
import { isDisabledAdmin, itemsPrenet } from '@/components/Collaboration/components';
import type { CollaborationData } from '@/components/Collaboration/types';
import { fm2 } from '@/modules/Locale';

import type { TreeNode } from '../hooks/useUserRole';
import { FrontRole } from '../hooks/useUserRole';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { isParentCollaborator } from '../utils/mobile';
import {
  CollaboratorCardButton,
  CollaboratorCardSpan,
  CollaboratorEmpty,
  CollarboratorCard,
  CollarboratorCardAvatar,
  CollarboratorCardContent,
  CollarboratorCardLeft,
  CollarboratorCardName,
  CollarboratorCardRole,
} from './Collaborator';
import { CollaboratorDeptUerItem, PermissionButton } from './CollaboratorUserItem';
import { MobileItemAddRole } from './const';

export type PerMissionType = 'admin' | 'collaborator' | 'parent';

export type TUserInfo = {
  id?: number;
  avatar: string | React.ReactNode;
  name: string;
  email?: string;
  departmentId?: number;
  role: string;
};

export type CollaboratorItemProps = {
  avatar: string | React.ReactNode;
  name: string;
  email?: string;
  showBtn: boolean;
  displayText: string;
  isDept?: boolean;
  onClick?: () => void;
};

export function ItemAvatar({ avatar, isDept }: { avatar: string | React.ReactNode; isDept?: boolean }) {
  if (isValidElement(avatar)) {
    return avatar;
  }
  return isDept ? <Organization height={32} width={32} /> : <CollarboratorCardAvatar avatar={avatar as string} />;
}

export function ItemButton({
  showBtn,
  displayText,
  onClick,
  icon = true,
}: {
  showBtn: boolean;
  displayText: string;
  onClick?: () => void;
  icon?: boolean;
}) {
  return showBtn ? (
    <CollaboratorCardButton icon={icon} text={displayText} onClick={onClick} />
  ) : (
    <CollaboratorCardSpan text={displayText} />
  );
}

export function AdminItem({ showBtn, displayText, avatar, name, email, isDept, onClick }: CollaboratorItemProps) {
  return (
    <CollarboratorCardContent>
      <CollarboratorCardLeft>
        <ItemAvatar avatar={avatar} isDept={isDept} />
        <CollarboratorCardName email={email} name={name} />
      </CollarboratorCardLeft>
      <ItemButton displayText={displayText} showBtn={showBtn} onClick={onClick} />
    </CollarboratorCardContent>
  );
}

export function AdminList({ data }: { data?: Role[] }) {
  const { collaborationData, refreshNum } = useCollaborationStore((state) => state);

  const nodeList = useMemo(() => {
    return (
      data?.map(
        (item) =>
          ({
            id: item.id,
            name: item.name,
            email: item.email,
            avatar: item.avatar,
            level: 0,
            displayText: MobileItemAddRole().find((k) => k.key === 'merger')?.label || '',
            showBtn: !isDisabledAdmin(collaborationData!, item, data),
            icon: true,
            frontRole: 'admin' as const,
            type: item.departmentId ? 'department' : 'user',
            role: item.role,
          }) as TreeNode,
      ) || []
    );
  }, [collaborationData, data]);

  return (
    <CollarboratorCard key={refreshNum}>
      {nodeList && nodeList.length > 0 ? (
        nodeList.map((item) => <CollaboratorDeptUerItem key={item.id} node={item} />)
      ) : (
        <CollaboratorEmpty />
      )}
    </CollarboratorCard>
  );
}

export function ParentCollaborator({
  data,
  open,
  showBtn,
}: {
  data: CollaborationData;
  showBtn: boolean;
  open?: (id: string) => void;
}) {
  const showCollaborator = useCallback(() => {
    if (open && data?.guid) {
      open(data.guid);
    }
  }, [data, open]);

  const displayText = useMemo(
    () => itemsPrenet().find((item) => item.key === data.parentRole)?.label || '',
    [data.parentRole],
  );

  const node = useMemo(
    () => ({
      id: 0,
      name: fm2('ShareCollaborationMobile.parentDirectoryCollaborator'),
      email: fm2('ShareCollaborationMobile.clickToViewCollaborators'),
      type: 'department' as const,
      level: 0,
      displayText,
      showBtn,
      icon: true,
      frontRole: FrontRole.parent,
      role: data.parentRole!,
    }),
    [data.parentRole, displayText, showBtn],
  );

  return (
    <CollarboratorCardContent>
      <CollarboratorCardLeft>
        <ItemAvatar avatar={<SuperiorOrganization />} />
        <CollarboratorCardRole
          name={fm2('ShareCollaborationMobile.parentDirectoryCollaborator')}
          open={showCollaborator}
        />
      </CollarboratorCardLeft>
      <PermissionButton defaultNode={node} />
    </CollarboratorCardContent>
  );
}

export function CollaboratorList({ data, open }: { data?: Role[]; open?: (id: string) => void }) {
  const { collaborationData, share, refreshNum } = useCollaborationStore((state) => state);

  const showParent = !!isParentCollaborator(collaborationData);

  const nodeList = useMemo(() => {
    return (
      data?.map(
        (item) =>
          ({
            id: item.id,
            name: item.name,
            email: item.email,
            avatar: item.avatar,
            level: 0,
            displayText: MobileItemAddRole().find((k) => k.key === item.role)?.label || '',
            showBtn: share?.canManageAdmin,
            icon: true,
            frontRole: 'collaborator' as const,
            type: item.departmentId ? 'department' : 'user',
            role: item.role,
          }) as TreeNode,
      ) || []
    );
  }, [data, share?.canManageAdmin]);

  const isEmpty = useMemo(() => {
    return (!data || data.length === 0) && !showParent;
  }, [data, showParent]);

  return (
    <CollarboratorCard key={refreshNum}>
      {isEmpty ? (
        <CollaboratorEmpty />
      ) : (
        <>
          {showParent && (
            <ParentCollaborator data={collaborationData!} open={open} showBtn={!!share?.canManageCollaborator} />
          )}
          {nodeList?.map((item) => <CollaboratorDeptUerItem key={item.id} node={item} />)}
        </>
      )}
    </CollarboratorCard>
  );
}
