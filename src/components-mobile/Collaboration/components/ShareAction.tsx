import classNames from 'classnames';
import { useEffect, useState } from 'react';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { usePermissions } from '@/components/Collaboration/hooks/usePermission';
import type { CollaborationData } from '@/components/Collaboration/types';
import { useDisclosure } from '@/hooks/useDisclosure';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import styles from './ShareAction.less';
import { ShareControll } from './ShareControll';

type ShareContentProps = {
  title: string;
  children?: React.ReactNode;
  className?: string;
};

export function ShareContentDefault({ title, children, className }: ShareContentProps) {
  return (
    <div className={classNames(styles.shareActionContent, className)}>
      <div className={styles.shareActionContentTitleDefault}>{title}</div>
      <div className={styles.shareActionContentText}>{children}</div>
    </div>
  );
}

export function ShareContentMedium({ title, children, className }: ShareContentProps) {
  return (
    <div className={classNames(styles.shareActionContent, className)}>
      <div className={styles.shareActionTitle}>{title}</div>
      {children && <div className={styles.shareActionContentText}>{children}</div>}
    </div>
  );
}
export function ShareSettings({
  title,
  children,
  highlightText,
  disable,
  onClick,
}: {
  title: string;
  highlightText: string;
  children?: React.ReactNode;
  disable?: boolean;
  onClick?: () => void;
}) {
  return (
    <div
      className={classNames(styles.shareSettings, {
        [styles.shareSettingsDisable]: disable,
      })}
      onClick={onClick}
    >
      <div className={styles.shareSettingsContent}>
        <div className={styles.shareSettingsTitle}>{title}</div>
        <span className={styles.shareSettingsTextSpan}>{highlightText}</span>
      </div>
      {children}
    </div>
  );
}

function ShareOpenContent() {
  return (
    <ShareContentMedium title="链接分享已开启">
      <span>
        企业内获得链接的人<span className={styles.shareActionTextSpan}>只能阅读</span>
      </span>
    </ShareContentMedium>
  );
}

type ShareActionProps = {
  data?: CollaborationData;
  toggleParentVisible: () => void;
  guid: string;
};

export function ShareAction({ data, toggleParentVisible, guid }: ShareActionProps) {
  const {
    password,
    shareStatus,
    shareDisabled,
    passwordStatus,
    shareTimeStatus,
    canManageCollaborator,
    canManageAdmin,
  } = usePermissions(data);
  const { isOpen, open, close, toggle } = useDisclosure(false);
  const [checked, setChecked] = useState(shareStatus);
  const setShare = useCollaborationStore((state) => state.setShare);

  function handleClick() {
    toggleParentVisible();
    open();
  }

  function handleClose() {
    close();
    toggleParentVisible();
  }

  useEffect(() => {
    setChecked(shareStatus);
  }, [shareStatus]);

  useEffect(() => {
    setShare({
      passwordStatus,
      shareTimeStatus,
      password,
      shareDisabled,
      canManageCollaborator,
      canManageAdmin,
    });
  }, [shareDisabled, password, passwordStatus, setShare, shareTimeStatus, canManageCollaborator, canManageAdmin]);

  return (
    <>
      <div className={styles.shareAction} onClick={handleClick}>
        {checked ? (
          <ShareOpenContent />
        ) : (
          <ShareContentMedium title="链接分享已关闭">文件的协作者和管理员仍可访问</ShareContentMedium>
        )}
        <ArrowRight />
      </div>
      <ShareControll
        guid={guid}
        open={checked}
        setOpen={setChecked}
        toggle={toggle}
        visible={isOpen}
        onClose={handleClose}
      />
    </>
  );
}
