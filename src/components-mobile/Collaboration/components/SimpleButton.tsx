import { Button } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { copyLink } from '../utils/mobile';
import styles from './ShareAction.less';

export function SimpleButton({
  onClick,
  children,
  disabled,
  ...rest
}: { onClick: () => void; children: React.ReactNode; disabled?: boolean } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={styles.simple} {...rest}>
      <Button className={styles.simpleButton} disabled={disabled} fill="none" onClick={onClick}>
        {children}
      </Button>
    </div>
  );
}

export function CopyLink({ onClick, disabled }: { onClick: () => void; disabled?: boolean }) {
  const { collaborationData: data, share } = useCollaborationStore((state) => state);
  const [text, setText] = useState('复制链接');

  function handleClick() {
    if (data?.url && data?.name) {
      const url = copyLink({ url: data.url, name: data.name });
      const copyUrl = share?.passwordStatus
        ? `${url} ${fm2('ShareCollaboration.accessPassword')} ${share?.password}`
        : url;
      handleCopyLinkShare(copyUrl);
      onClick();
    }
  }

  useEffect(() => {
    if (share?.passwordStatus) {
      setText('复制链接和密码');
    } else {
      setText('复制链接');
    }
  }, [share?.passwordStatus]);

  return (
    <SimpleButton disabled={disabled} onClick={handleClick}>
      {text}
    </SimpleButton>
  );
}

export function CloseAction({
  onClick,
  title,
  ...rest
}: {
  onClick: () => void;
  title?: string;
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <SimpleButton onClick={onClick} {...rest}>
      {title || '关闭'}
    </SimpleButton>
  );
}
