import { CaretDownOutlined } from '@ant-design/icons';
import { Avatar } from 'antd-mobile';
import classNames from 'classnames';

import { fm } from '@/modules/Locale';

import styles from './CollaboratorManagement.less';

export function CollarboratorCard({ children }: { children: React.ReactNode }) {
  return <div className={styles.section}>{children}</div>;
}

export function CollarboratorCardContent({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={classNames(styles.userItem, className)}>{children}</div>;
}

export function CollarboratorCardLeft({ children }: { children: React.ReactNode }) {
  return <div className={styles.userInfo}>{children}</div>;
}

export function CollarboratorCardAvatar({ avatar }: { avatar: string }) {
  return <Avatar className={styles.userAvatar} src={avatar} />;
}

export function CollarboratorCardName({
  name,
  email,
  className,
}: {
  name: string;
  email?: string;
  className?: string;
}) {
  return (
    <div className={classNames(styles.userDetails, className)}>
      <div className={styles.userName}>{name}</div>
      {email && <div className={styles.userEmail}>{email}</div>}
    </div>
  );
}

export function CollarboratorCardRole({ name, email, open }: { name: string; email?: string; open?: () => void }) {
  return (
    <div className={styles.userDetails}>
      <div className={styles.userName}>{name || email}</div>
      <div className={styles.watchUser} onClick={open}>
        点击查看协作者
      </div>
    </div>
  );
}

export function CollaboratorCardSpan({ text }: { text: string }) {
  return <div className={styles.userRole}>{text}</div>;
}

export function CollaboratorCardButton({
  text,
  onClick,
  icon = true,
}: {
  text: string;
  onClick?: () => void;
  icon?: boolean;
}) {
  return (
    <div className={styles.itemButton} onClick={onClick}>
      <span>{text}</span>
      {icon && <CaretDownOutlined className={styles.userRoleIcon} />}
    </div>
  );
}

export function CollaboratorEmpty() {
  return (
    <div className={styles.emptyState}>
      <div className={styles.emptyText}>{fm('ShareCollaboration.noCollaborators')}</div>
    </div>
  );
}
